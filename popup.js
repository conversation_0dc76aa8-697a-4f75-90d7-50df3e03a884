// popup.js - 插件弹窗逻辑
class LBSCollectorPopup {
  constructor() {
    this.isCollecting = false;
    this.collectedData = [];
    this.init();
  }

  init() {
    this.bindEvents();
    this.loadStoredData();
    this.updateUI();
  }

  bindEvents() {
    document.getElementById('startCollection').addEventListener('click', () => this.startCollection());
    document.getElementById('stopCollection').addEventListener('click', () => this.stopCollection());
    document.getElementById('exportCsv').addEventListener('click', () => this.exportCsv());
    document.getElementById('clearData').addEventListener('click', () => this.clearData());
    
    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
  }

  async loadStoredData() {
    try {
      const result = await chrome.storage.local.get(['lbsData', 'isCollecting']);
      this.collectedData = result.lbsData || [];
      this.isCollecting = result.isCollecting || false;
      this.updateUI();
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  }

  async saveData() {
    try {
      await chrome.storage.local.set({
        lbsData: this.collectedData,
        isCollecting: this.isCollecting
      });
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  async startCollection() {
    const keyword = document.getElementById('keyword').value.trim();
    if (!keyword) {
      this.showStatus('请输入搜索关键词', 'error');
      return;
    }

    // 检查当前标签页是否是Google地图
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.url.includes('google.com/maps') && !tab.url.includes('maps.google.com')) {
      this.showStatus('请先打开Google地图页面', 'error');
      return;
    }

    this.isCollecting = true;
    await this.saveData();
    
    this.showStatus('开始采集数据...', 'info');
    this.updateUI();

    // 发送消息给content script开始采集
    try {
      await chrome.tabs.sendMessage(tab.id, {
        action: 'startCollection',
        keyword: keyword,
        location: document.getElementById('location').value.trim()
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      this.showStatus('启动采集失败，请刷新页面后重试', 'error');
      this.isCollecting = false;
      await this.saveData();
      this.updateUI();
    }
  }

  async stopCollection() {
    this.isCollecting = false;
    await this.saveData();
    
    this.showStatus('已停止采集', 'info');
    this.updateUI();

    // 通知content script停止采集
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.sendMessage(tab.id, { action: 'stopCollection' });
    } catch (error) {
      console.error('停止采集消息发送失败:', error);
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'dataCollected':
        this.addCollectedData(message.data);
        break;
      case 'collectionComplete':
        this.isCollecting = false;
        this.saveData();
        this.showStatus(`采集完成！共采集到 ${this.collectedData.length} 条数据`, 'success');
        this.updateUI();
        break;
      case 'collectionError':
        this.isCollecting = false;
        this.saveData();
        this.showStatus(`采集出错: ${message.error}`, 'error');
        this.updateUI();
        break;
    }
  }

  addCollectedData(newData) {
    // 更严格的重复数据检查
    const exists = this.collectedData.some(item => {
      const nameMatch = item.name === newData.name;
      const addressMatch = item.address === newData.address;
      const phoneMatch = item.phone && newData.phone && item.phone === newData.phone;

      return nameMatch && (addressMatch || phoneMatch);
    });

    if (!exists && newData.name && newData.name.trim()) {
      // 数据清理和标准化
      const cleanedData = this.cleanData(newData);
      this.collectedData.push(cleanedData);
      this.saveData();
      this.updateUI();
      this.showStatus(`已采集 ${this.collectedData.length} 条数据`, 'info');
    }
  }

  cleanData(data) {
    return {
      name: this.cleanText(data.name),
      address: this.cleanText(data.address),
      phone: this.cleanPhone(data.phone),
      rating: this.cleanRating(data.rating),
      reviewCount: this.cleanReviewCount(data.reviewCount),
      type: this.cleanText(data.type),
      website: this.cleanUrl(data.website),
      hours: this.cleanText(data.hours),
      timestamp: data.timestamp || new Date().toISOString()
    };
  }

  cleanText(text) {
    if (!text) return '';
    return text.trim().replace(/\s+/g, ' ');
  }

  cleanPhone(phone) {
    if (!phone) return '';
    // 移除非数字字符，保留 + - ( ) 空格
    return phone.replace(/[^\d\+\-\(\)\s]/g, '').trim();
  }

  cleanRating(rating) {
    if (!rating) return '';
    const match = rating.match(/(\d+\.?\d*)/);
    return match ? match[1] : '';
  }

  cleanReviewCount(reviewCount) {
    if (!reviewCount) return '';
    // 提取数字
    const match = reviewCount.match(/(\d+)/);
    return match ? match[1] : '';
  }

  cleanUrl(url) {
    if (!url) return '';
    try {
      new URL(url);
      return url;
    } catch {
      return url.startsWith('http') ? url : '';
    }
  }

  updateUI() {
    const startBtn = document.getElementById('startCollection');
    const stopBtn = document.getElementById('stopCollection');
    const exportBtn = document.getElementById('exportCsv');
    const dataSummary = document.getElementById('dataSummary');
    const dataCount = document.getElementById('dataCount');
    const dataPreview = document.getElementById('dataPreview');
    const progressFill = document.getElementById('progressFill');

    // 更新按钮状态
    startBtn.disabled = this.isCollecting;
    stopBtn.disabled = !this.isCollecting;
    exportBtn.disabled = this.collectedData.length === 0;

    // 更新按钮文本
    if (this.isCollecting) {
      startBtn.textContent = '采集中...';
      stopBtn.textContent = '停止采集';
    } else {
      startBtn.textContent = '开始采集';
      stopBtn.textContent = '停止采集';
    }

    // 更新数据摘要
    if (this.collectedData.length > 0) {
      dataSummary.style.display = 'block';

      // 数据统计
      const stats = this.generateDataStats();
      dataCount.innerHTML = `
        <div>已采集: <strong>${this.collectedData.length}</strong> 条数据</div>
        <div style="font-size: 12px; color: #666; margin-top: 5px;">
          📞 ${stats.withPhone}个有电话 | ⭐ ${stats.withRating}个有评分 | 🌐 ${stats.withWebsite}个有网站
        </div>
      `;

      // 进度条 (模拟进度，基于采集数量)
      const progress = Math.min((this.collectedData.length / 50) * 100, 100);
      progressFill.style.width = progress + '%';

      // 更新数据预览 - 显示最新的5条数据
      const recentData = this.collectedData.slice(-5).reverse();
      dataPreview.innerHTML = recentData.map((item, index) => {
        const completeness = this.calculateCompleteness(item);
        const completenessColor = completeness >= 80 ? '#34a853' : completeness >= 60 ? '#fbbc04' : '#ea4335';

        return `
          <div class="data-item">
            <div class="business-name">
              ${item.name || '未知商家'}
              <span style="float: right; font-size: 10px; color: ${completenessColor};">
                ${completeness}%
              </span>
            </div>
            <div class="business-details">
              📍 ${this.truncateText(item.address || '地址未知', 30)}<br>
              📞 ${item.phone || '电话未知'}<br>
              ⭐ ${item.rating || '暂无评分'} ${item.reviewCount ? `(${item.reviewCount}条评论)` : ''}
              ${item.type ? `<br>🏷️ ${item.type}` : ''}
            </div>
          </div>
        `;
      }).join('');
    } else {
      dataSummary.style.display = 'none';
    }

    // 更新导出按钮文本
    if (this.collectedData.length > 0) {
      exportBtn.textContent = `导出CSV (${this.collectedData.length}条)`;
    } else {
      exportBtn.textContent = '导出CSV';
    }
  }

  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  showStatus(message, type) {
    const statusEl = document.getElementById('status');
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
      statusEl.style.display = 'none';
    }, 3000);
  }

  async exportCsv() {
    if (this.collectedData.length === 0) {
      this.showStatus('没有数据可导出', 'error');
      return;
    }

    try {
      const csvContent = this.generateCsv();
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `lbs_data_${timestamp}.csv`;
      
      await chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: true
      });
      
      this.showStatus(`已导出 ${this.collectedData.length} 条数据`, 'success');
    } catch (error) {
      console.error('导出失败:', error);
      this.showStatus('导出失败，请重试', 'error');
    }
  }

  generateCsv() {
    const headers = [
      '商家名称',
      '地址',
      '电话',
      '评分',
      '评论数',
      '商家类型',
      '网站',
      '营业时间',
      '采集时间',
      '数据完整度'
    ];
    const csvRows = [headers.join(',')];

    // 添加数据统计行
    const stats = this.generateDataStats();
    csvRows.push(''); // 空行
    csvRows.push('数据统计信息');
    csvRows.push(`总数据量,${stats.total}`);
    csvRows.push(`有电话号码,${stats.withPhone}`);
    csvRows.push(`有评分,${stats.withRating}`);
    csvRows.push(`有网站,${stats.withWebsite}`);
    csvRows.push(`平均评分,${stats.avgRating}`);
    csvRows.push(''); // 空行
    csvRows.push('详细数据');
    csvRows.push(headers.join(','));

    this.collectedData.forEach(item => {
      const completeness = this.calculateCompleteness(item);
      const row = [
        this.escapeCsvField(item.name || ''),
        this.escapeCsvField(item.address || ''),
        this.escapeCsvField(item.phone || ''),
        this.escapeCsvField(item.rating || ''),
        this.escapeCsvField(item.reviewCount || ''),
        this.escapeCsvField(item.type || ''),
        this.escapeCsvField(item.website || ''),
        this.escapeCsvField(item.hours || ''),
        this.escapeCsvField(item.timestamp || ''),
        this.escapeCsvField(completeness + '%')
      ];
      csvRows.push(row.join(','));
    });

    return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
  }

  generateDataStats() {
    const total = this.collectedData.length;
    const withPhone = this.collectedData.filter(item => item.phone && item.phone.trim()).length;
    const withRating = this.collectedData.filter(item => item.rating && item.rating.trim()).length;
    const withWebsite = this.collectedData.filter(item => item.website && item.website.trim()).length;

    const ratings = this.collectedData
      .filter(item => item.rating && !isNaN(parseFloat(item.rating)))
      .map(item => parseFloat(item.rating));

    const avgRating = ratings.length > 0
      ? (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(2)
      : '无';

    return {
      total,
      withPhone,
      withRating,
      withWebsite,
      avgRating
    };
  }

  calculateCompleteness(item) {
    const fields = ['name', 'address', 'phone', 'rating', 'type'];
    const filledFields = fields.filter(field => item[field] && item[field].trim()).length;
    return Math.round((filledFields / fields.length) * 100);
  }

  escapeCsvField(field) {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  async clearData() {
    if (confirm('确定要清空所有采集的数据吗？')) {
      this.collectedData = [];
      await this.saveData();
      this.updateUI();
      this.showStatus('数据已清空', 'info');
    }
  }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
  new LBSCollectorPopup();
});
