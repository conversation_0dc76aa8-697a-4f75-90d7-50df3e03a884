<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #1a73e8;
        }
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #1a73e8;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        .code-block {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1557b0; }
        .checklist {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 搜索功能测试页面</h1>
    
    <div class="test-section">
        <h2>🎯 测试目标</h2>
        <p>验证修复后的搜索功能能够：</p>
        <ul>
            <li>✅ 正确找到Google Maps搜索框</li>
            <li>✅ 成功输入搜索关键词</li>
            <li>✅ 正确提交搜索请求</li>
            <li>✅ 验证搜索是否执行</li>
            <li>✅ 提供手动搜索备用方案</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📋 测试步骤</h2>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>准备测试环境</strong>
            <p>确保插件已重新加载并启用</p>
            <button onclick="openGoogleMaps()">打开Google Maps</button>
            <button onclick="checkPluginStatus()">检查插件状态</button>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <strong>启动搜索测试</strong>
            <p>在Google Maps页面中启动LBS采集器</p>
            <div class="code-block">
                测试关键词: "咖啡店"
                测试地区: "北京"
            </div>
            <div class="status warning">
                注意观察控制台日志输出，特别是搜索相关的日志
            </div>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <strong>观察搜索过程</strong>
            <p>在控制台中查看以下关键日志：</p>
            <div class="log-output" id="expected-logs">
🔍 [LBS采集器] 准备搜索: "咖啡店 北京"
🔍 [LBS采集器] 查找搜索框...
✅ [LBS采集器] 找到搜索框: {tagName: "INPUT", id: "searchboxinput", ...}
🧹 [LBS采集器] 清空搜索框...
📝 [LBS采集器] 搜索框已清空，当前值: ""
⌨️ [LBS采集器] 开始输入搜索内容...
📝 [LBS采集器] 方法1完成，当前值: "咖啡店 北京"
🚀 [LBS采集器] 尝试提交搜索...
🚀 [LBS采集器] 方法1: 按Enter键...
✅ [LBS采集器] 搜索提交完成: "咖啡店 北京"
🔍 [LBS采集器] 验证搜索是否执行...
✅ [LBS采集器] URL验证通过：包含搜索参数
✅ [LBS采集器] 搜索框验证通过：包含关键词
✅ [LBS采集器] 搜索验证通过，继续等待结果...
            </div>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <strong>处理搜索失败情况</strong>
            <p>如果自动搜索失败，插件会显示手动搜索选项：</p>
            <div class="status error">
                自动搜索失败，请按以下步骤操作：<br>
                1. 在Google Maps搜索框中输入: "咖啡店 北京"<br>
                2. 按Enter或点击搜索按钮<br>
                3. 等待搜索结果显示<br>
                4. 点击"继续采集"按钮
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 手动测试工具</h2>
        
        <div class="step">
            <strong>搜索框检测工具</strong>
            <p>在Google Maps页面的控制台中运行以下代码：</p>
            <div class="code-block" id="search-box-test">
// 检测搜索框
const searchSelectors = [
    'input[data-value="Search"]',
    '#searchboxinput',
    'input[aria-label*="Search"]',
    'input[aria-label*="搜索"]'
];

console.log('🔍 搜索框检测结果:');
searchSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    console.log(`${selector}: ${element ? '✅ 找到' : '❌ 未找到'}`);
    if (element) {
        console.log('  详细信息:', {
            id: element.id,
            className: element.className,
            placeholder: element.placeholder,
            value: element.value
        });
    }
});
            </div>
            <button onclick="copyCode('search-box-test')">复制代码</button>
        </div>

        <div class="step">
            <strong>搜索执行测试</strong>
            <p>手动测试搜索功能：</p>
            <div class="code-block" id="search-execution-test">
// 手动执行搜索
const searchBox = document.querySelector('#searchboxinput') || 
                 document.querySelector('input[data-value="Search"]');

if (searchBox) {
    console.log('✅ 找到搜索框');
    
    // 清空并输入
    searchBox.value = '';
    searchBox.value = '咖啡店 北京';
    
    // 触发事件
    searchBox.dispatchEvent(new Event('input', { bubbles: true }));
    searchBox.dispatchEvent(new KeyboardEvent('keydown', { 
        key: 'Enter', 
        bubbles: true 
    }));
    
    console.log('🚀 搜索已提交');
    
    // 检查结果
    setTimeout(() => {
        console.log('当前URL:', window.location.href);
        console.log('搜索框值:', searchBox.value);
    }, 2000);
} else {
    console.error('❌ 未找到搜索框');
}
            </div>
            <button onclick="copyCode('search-execution-test')">复制代码</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试结果记录</h2>
        
        <div class="checklist">
            <h3>测试清单:</h3>
            <ul id="test-results">
                <li>⏳ 插件正常加载</li>
                <li>⏳ 悬浮按钮显示</li>
                <li>⏳ 找到搜索框</li>
                <li>⏳ 成功输入关键词</li>
                <li>⏳ 搜索请求提交</li>
                <li>⏳ 搜索验证通过</li>
                <li>⏳ 搜索结果加载</li>
                <li>⏳ 数据采集开始</li>
            </ul>
        </div>
        
        <div>
            <button onclick="markTestPassed()">标记测试通过</button>
            <button onclick="markTestFailed()">标记测试失败</button>
            <button onclick="resetTestResults()">重置测试结果</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🚨 常见问题及解决方案</h2>
        
        <div class="step">
            <strong>问题1: 找不到搜索框</strong>
            <div class="status error">
                错误: 未找到搜索框，请确保在Google地图页面
            </div>
            <p><strong>解决方案:</strong></p>
            <ul>
                <li>确认在正确的Google Maps页面 (maps.google.com)</li>
                <li>等待页面完全加载</li>
                <li>刷新页面后重试</li>
                <li>检查是否有其他元素遮挡搜索框</li>
            </ul>
        </div>

        <div class="step">
            <strong>问题2: 搜索未执行</strong>
            <div class="status warning">
                警告: 页面URL未变化，搜索可能未执行
            </div>
            <p><strong>解决方案:</strong></p>
            <ul>
                <li>使用手动搜索备用方案</li>
                <li>检查网络连接</li>
                <li>尝试不同的关键词</li>
                <li>手动在搜索框中输入并按Enter</li>
            </ul>
        </div>

        <div class="step">
            <strong>问题3: 搜索结果加载超时</strong>
            <div class="status error">
                错误: 搜索结果加载超时
            </div>
            <p><strong>解决方案:</strong></p>
            <ul>
                <li>检查网络连接速度</li>
                <li>尝试更具体的关键词</li>
                <li>等待更长时间后重试</li>
                <li>使用"继续采集"功能</li>
            </ul>
        </div>
    </div>

    <script>
        function openGoogleMaps() {
            window.open('https://maps.google.com', '_blank');
        }

        function checkPluginStatus() {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                alert('✅ 插件API可用');
            } else {
                alert('❌ 插件API不可用，请检查插件是否已安装');
            }
        }

        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('代码已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        function markTestPassed() {
            const items = document.querySelectorAll('#test-results li');
            items.forEach(item => {
                item.textContent = item.textContent.replace('⏳', '✅');
                item.style.background = '#d4edda';
                item.style.color = '#155724';
            });
        }

        function markTestFailed() {
            const items = document.querySelectorAll('#test-results li');
            items.forEach(item => {
                item.textContent = item.textContent.replace('⏳', '❌');
                item.style.background = '#f8d7da';
                item.style.color = '#721c24';
            });
        }

        function resetTestResults() {
            const items = document.querySelectorAll('#test-results li');
            items.forEach(item => {
                item.textContent = item.textContent.replace(/[✅❌]/, '⏳');
                item.style.background = '';
                item.style.color = '';
            });
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('🔍 搜索功能测试页面已加载');
            console.log('请按照页面指引进行测试');
        };
    </script>
</body>
</html>
