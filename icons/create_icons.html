<!DOCTYPE html>
<html>
<head>
    <title>创建插件图标</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <canvas id="canvas128" width="128" height="128"></canvas>
    
    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 背景
            ctx.fillStyle = '#1a73e8';
            ctx.fillRect(0, 0, size, size);
            
            // 地图图标
            ctx.fillStyle = '#ffffff';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🗺️', size/2, size/2);
            
            // 下载图标
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 创建所有尺寸的图标
        setTimeout(() => {
            createIcon('canvas16', 16);
            createIcon('canvas48', 48);
            createIcon('canvas128', 128);
        }, 1000);
    </script>
</body>
</html>
