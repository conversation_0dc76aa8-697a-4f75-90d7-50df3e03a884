# 悬浮UI版本使用指南

## 🎯 界面概览

### 悬浮按钮
- **位置**: 页面右上角
- **外观**: 蓝色圆形按钮，显示 🗺️ 图标
- **状态指示**:
  - "LBS采集" - 初始状态
  - "采集中" - 正在采集数据（绿色）
  - "X条数据" - 显示已采集数量

### 主面板
- **展开方式**: 点击悬浮按钮
- **组成部分**:
  - 标题栏（可拖拽）
  - 搜索输入区域
  - 控制按钮
  - 数据预览区域
  - 状态显示

## 🖱️ 交互操作

### 基础操作
| 操作 | 功能 |
|------|------|
| 单击悬浮按钮 | 展开/收缩面板 |
| 双击悬浮按钮 | 快速开始采集 |
| 拖拽标题栏 | 移动面板位置 |
| 点击最小化(-) | 收缩为悬浮按钮 |
| 点击关闭(×) | 完全隐藏插件 |

### 快捷键
| 快捷键 | 功能 |
|--------|------|
| Ctrl+Shift+L | 切换面板显示 |
| ESC | 关闭面板 |

### 右键菜单
在面板上右键点击可显示上下文菜单：
- **重置位置**: 将面板移回右上角
- **切换自动隐藏**: 开启/关闭自动隐藏功能
- **导出设置**: 保存当前配置
- **关于插件**: 显示版本信息

## 📊 数据采集流程

### 1. 准备阶段
1. 打开 [Google Maps](https://maps.google.com)
2. 等待页面完全加载
3. 确认右上角出现悬浮按钮

### 2. 设置搜索
1. 点击悬浮按钮展开面板
2. 输入搜索关键词（必填）
3. 输入地区限制（可选）

### 3. 开始采集
1. 点击"开始采集"按钮
2. 插件自动执行搜索
3. 开始采集商家数据
4. 实时显示采集进度

### 4. 监控进度
- **数据计数**: 显示已采集数量
- **进度条**: 可视化采集进度
- **数据预览**: 显示最新5条数据
- **完整度**: 每条数据的完整度百分比

### 5. 导出数据
1. 采集完成或手动停止
2. 点击"导出CSV"按钮
3. 选择保存位置
4. 文件自动下载

## 🎨 界面特性

### 动画效果
- **展开动画**: 面板从右上角滑入
- **悬停效果**: 按钮和控件有悬停反馈
- **拖拽反馈**: 拖拽时面板轻微旋转

### 响应式设计
- **桌面端**: 350px宽度面板
- **移动端**: 自适应屏幕宽度
- **边界检测**: 防止拖拽到屏幕外

### 状态指示
- **信息提示**: 蓝色背景
- **成功提示**: 绿色背景
- **错误提示**: 红色背景
- **自动消失**: 3秒后自动隐藏

## 🔧 高级功能

### 自动隐藏
- **触发条件**: 30秒无操作
- **例外情况**: 正在采集时不会隐藏
- **恢复方式**: 鼠标悬停取消隐藏

### 位置记忆
- **自动保存**: 拖拽后自动记住位置
- **窗口调整**: 浏览器窗口变化时自动调整
- **边界保护**: 确保面板始终可见

### 数据管理
- **本地存储**: 数据保存在浏览器本地
- **去重处理**: 自动过滤重复商家
- **数据清理**: 标准化电话、地址等信息

## 🚨 注意事项

### 使用限制
- 仅在Google Maps页面有效
- 需要稳定的网络连接
- 建议控制采集频率

### 性能优化
- 大量数据时可能影响页面性能
- 建议分批采集，及时导出
- 定期清理历史数据

### 兼容性
- Chrome 88+ 版本
- 支持最新的Google Maps界面
- 可能需要根据Maps更新调整

## 🔍 故障排除

### 悬浮按钮不显示
1. 刷新Google Maps页面
2. 检查插件是否启用
3. 确认在正确的Maps页面

### 数据采集失败
1. 检查网络连接
2. 确认搜索关键词有效
3. 查看浏览器控制台错误

### 面板无法拖拽
1. 确保拖拽标题栏区域
2. 检查是否有其他元素遮挡
3. 尝试刷新页面重新加载

### CSV导出问题
1. 检查浏览器下载权限
2. 确认有数据可导出
3. 尝试更换保存位置

## 📝 使用技巧

### 提高采集效率
1. 使用具体的关键词
2. 添加地区限制缩小范围
3. 分时段采集避免高峰期

### 数据质量优化
1. 定期检查数据完整度
2. 手动验证重要信息
3. 及时清理无效数据

### 界面操作技巧
1. 双击悬浮按钮快速重复采集
2. 使用快捷键提高操作效率
3. 合理利用自动隐藏功能

---

**版本**: 1.0.0 (悬浮UI版本)  
**更新时间**: 2024-01-01  
**兼容性**: Chrome 88+, Google Maps 最新版
