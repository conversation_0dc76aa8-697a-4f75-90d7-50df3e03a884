<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      color: #1a73e8;
      font-size: 18px;
      margin: 0;
    }
    
    .search-section {
      margin-bottom: 20px;
    }
    
    .input-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
    }
    
    input[type="text"] {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    input[type="text"]:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    button {
      flex: 1;
      padding: 10px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background-color: #1a73e8;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #1557b0;
    }
    
    .btn-secondary {
      background-color: #f8f9fa;
      color: #333;
      border: 1px solid #ddd;
    }
    
    .btn-secondary:hover {
      background-color: #e8f0fe;
    }
    
    .btn-success {
      background-color: #34a853;
      color: white;
    }
    
    .btn-success:hover {
      background-color: #2d8f47;
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      font-size: 14px;
      display: none;
    }
    
    .status.info {
      background-color: #e8f0fe;
      color: #1a73e8;
      border: 1px solid #dadce0;
    }
    
    .status.success {
      background-color: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }
    
    .status.error {
      background-color: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }
    
    .data-summary {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 15px;
    }
    
    .data-count {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
    }
    
    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 10px;
    }
    
    .progress-fill {
      height: 100%;
      background-color: #1a73e8;
      width: 0%;
      transition: width 0.3s ease;
    }
    
    .data-preview {
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      font-size: 12px;
      background-color: white;
    }
    
    .data-item {
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .data-item:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .business-name {
      font-weight: 500;
      color: #333;
    }
    
    .business-details {
      color: #666;
      font-size: 11px;
      margin-top: 2px;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🗺️ LBS数据采集器</h1>
  </div>
  
  <div class="search-section">
    <div class="input-group">
      <label for="keyword">搜索关键词:</label>
      <input type="text" id="keyword" placeholder="例如: 餐厅, 咖啡店, 超市" />
    </div>
    
    <div class="input-group">
      <label for="location">地区 (可选):</label>
      <input type="text" id="location" placeholder="例如: 北京, 上海" />
    </div>
  </div>
  
  <div class="button-group">
    <button id="startCollection" class="btn-primary">开始采集</button>
    <button id="stopCollection" class="btn-secondary">停止采集</button>
  </div>
  
  <div id="status" class="status"></div>
  
  <div id="dataSummary" class="data-summary" style="display: none;">
    <div id="dataCount" class="data-count">已采集: 0 条数据</div>
    <div class="progress-bar">
      <div id="progressFill" class="progress-fill"></div>
    </div>
    <div id="dataPreview" class="data-preview"></div>
  </div>
  
  <div class="button-group">
    <button id="exportCsv" class="btn-success" disabled>导出CSV</button>
    <button id="clearData" class="btn-secondary">清空数据</button>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
