# Google Maps LBS数据采集器

一个Chrome浏览器插件，用于从Google地图采集商家LBS（Location Based Service）数据，支持CSV格式导出。

## 功能特性

- 🔍 **关键词搜索**: 支持按关键词搜索商家
- 📍 **地区筛选**: 可指定特定地区进行搜索
- 📊 **数据采集**: 自动采集商家详细信息
- 💾 **数据存储**: 本地存储采集的数据
- 📁 **CSV导出**: 支持将数据导出为CSV文件
- 🎯 **去重处理**: 自动去除重复的商家数据

## 采集的数据字段

- 商家名称
- 地址
- 电话号码
- 评分
- 评论数量
- 商家类型
- 网站地址
- 营业时间

## 安装方法

### 开发者模式安装

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹
6. 插件安装完成

## 使用方法

### 1. 打开Google地图
访问 [Google Maps](https://maps.google.com)

### 2. 启动插件
- 点击浏览器工具栏中的插件图标
- 在弹出窗口中输入搜索关键词（如：餐厅、咖啡店、超市等）
- 可选择性输入地区限制（如：北京、上海等）

### 3. 开始采集
- 点击"开始采集"按钮
- 插件会自动在Google地图中搜索并采集数据
- 实时显示采集进度和数据预览

### 4. 导出数据
- 采集完成后，点击"导出CSV"按钮
- 选择保存位置，数据将以CSV格式保存

## 注意事项

⚠️ **使用须知**
- 请遵守Google Maps的使用条款
- 建议合理控制采集频率，避免过于频繁的请求
- 采集的数据仅供个人学习和研究使用
- 商业用途请确保符合相关法律法规

⚠️ **技术限制**
- 需要在Google Maps页面中使用
- 采集速度受网络状况影响
- 部分商家信息可能不完整

## 故障排除

### 常见问题

**Q: 插件无法启动采集**
A: 请确保当前页面是Google Maps，并刷新页面后重试

**Q: 采集的数据不完整**
A: Google Maps的页面结构可能发生变化，或者网络加载较慢

**Q: 无法导出CSV文件**
A: 检查浏览器的下载权限设置

### 技术支持

如遇到问题，请检查：
1. Chrome浏览器版本是否为最新
2. 插件权限是否正确授予
3. 网络连接是否正常

## 开发信息

- **版本**: 1.0.0
- **兼容性**: Chrome 88+
- **开发语言**: JavaScript
- **框架**: Chrome Extension Manifest V3

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基础的LBS数据采集功能
- 实现CSV导出功能
- 添加数据去重和存储功能

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规和服务条款。
