# 悬浮UI版本故障排除指南

## 🚨 常见问题及解决方案

### 1. 悬浮按钮不显示

**症状**: 在Google Maps页面看不到右上角的蓝色悬浮按钮

**可能原因及解决方案**:
- ✅ **检查插件状态**: 确认插件已安装并启用
- ✅ **刷新页面**: 按F5或Ctrl+R刷新Google Maps页面
- ✅ **检查页面URL**: 确保在正确的Google Maps页面 (maps.google.com)
- ✅ **清除缓存**: 清除浏览器缓存后重新加载
- ✅ **检查控制台**: 按F12查看是否有JavaScript错误

### 2. 面板无法展开

**症状**: 点击悬浮按钮没有反应，面板不展开

**解决方案**:
- 🔄 **重新点击**: 等待1-2秒后再次点击
- 🖱️ **检查点击区域**: 确保点击在按钮中心
- ⌨️ **使用快捷键**: 尝试 Ctrl+Shift+L
- 🔧 **重新加载插件**: 在chrome://extensions/中重新加载插件

### 3. 拖拽功能不工作

**症状**: 无法拖拽移动面板位置

**解决方案**:
- 🎯 **拖拽正确区域**: 只能拖拽蓝色标题栏
- 🖱️ **检查鼠标**: 确保鼠标左键正常工作
- 📱 **避免其他元素**: 确保没有其他页面元素遮挡
- 🔄 **重新展开**: 收缩后重新展开面板

### 4. 数据采集失败

**症状**: 点击"开始采集"后没有数据或报错

**错误信息**: `TypeError: this.sleep is not a function`
**解决方案**:
- ✅ **已修复**: 最新版本已修复此问题
- 🔄 **重新安装**: 重新加载插件文件

**新增功能**:
- 📊 **详细日志**: 现在有详细的控制台日志输出
- 🔧 **调试面板**: 点击"显示调试"查看实时状态
- 🛑 **改进停止**: 修复了无法停止采集的问题

**其他采集问题**:
- 🔍 **检查关键词**: 确保输入了有效的搜索关键词
- 🌐 **网络连接**: 检查网络连接是否稳定
- 📍 **搜索结果**: 确认Google Maps有搜索结果
- ⏱️ **等待时间**: 给页面足够的加载时间

### 5. CSV导出失败

**症状**: 点击导出按钮没有反应或下载失败

**解决方案**:
- 📊 **检查数据**: 确认已采集到数据
- 📁 **下载权限**: 检查浏览器下载权限设置
- 💾 **存储空间**: 确保有足够的磁盘空间
- 🔄 **重试操作**: 等待几秒后重试

### 6. 快捷键不响应

**症状**: Ctrl+Shift+L 快捷键无效

**解决方案**:
- ⌨️ **按键顺序**: 先按Ctrl，再按Shift，最后按L
- 🎯 **焦点检查**: 确保焦点在Google Maps页面
- 🔧 **键盘检查**: 测试其他快捷键是否正常
- 🔄 **重新加载**: 刷新页面后重试

### 7. 面板位置异常

**症状**: 面板显示在屏幕外或位置不正确

**解决方案**:
- 🖱️ **右键菜单**: 右键点击面板选择"重置位置"
- 📐 **窗口调整**: 调整浏览器窗口大小
- 🔄 **重新加载**: 刷新页面恢复默认位置

## 🔧 高级故障排除

### 检查控制台错误

1. 在Google Maps页面按 **F12** 打开开发者工具
2. 切换到 **Console** 标签页
3. 查看是否有红色错误信息
4. 常见错误及解决方案:

```javascript
// 错误: Cannot read property 'querySelector' of null
// 解决: 等待页面完全加载后再操作

// 错误: Permission denied
// 解决: 检查插件权限设置

// 错误: Script error
// 解决: 重新安装插件
```

### 检查插件权限

1. 访问 `chrome://extensions/`
2. 找到"Google Maps LBS Data Collector"
3. 点击"详细信息"
4. 确认以下权限已授予:
   - ✅ 读取和更改您在 google.com 上的数据
   - ✅ 存储无限量的客户端数据
   - ✅ 管理您的下载内容

### 重新安装插件

如果问题持续存在，尝试重新安装:

1. 在 `chrome://extensions/` 中移除插件
2. 重新加载插件文件夹
3. 刷新Google Maps页面
4. 检查悬浮按钮是否出现

## 📊 性能优化建议

### 减少内存使用
- 🗑️ **定期清理**: 及时清空采集的数据
- 📊 **分批采集**: 避免一次采集过多数据
- 🔄 **重启浏览器**: 长时间使用后重启浏览器

### 提高采集效率
- 🎯 **精确关键词**: 使用具体的搜索词
- 📍 **地区限制**: 添加地区限制缩小范围
- ⏰ **避开高峰**: 避免在网络高峰期采集

## 📞 获取帮助

如果以上方案都无法解决问题:

1. **记录错误信息**: 截图保存错误信息
2. **描述操作步骤**: 详细描述出现问题的操作
3. **环境信息**: 提供Chrome版本和操作系统信息
4. **测试数据**: 提供测试用的关键词和地区

### 系统要求
- **Chrome版本**: 88+ (推荐最新版本)
- **操作系统**: Windows 10+, macOS 10.14+, Linux
- **网络**: 稳定的互联网连接
- **内存**: 建议4GB以上

---

**版本**: 1.0.0 (悬浮UI版本)  
**最后更新**: 2024-01-01  
**状态**: 已修复 sleep 函数错误
