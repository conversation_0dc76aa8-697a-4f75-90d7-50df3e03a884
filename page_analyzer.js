// Google Maps页面结构分析工具
// 在Google Maps页面的控制台中运行此脚本来分析页面结构

function analyzeGoogleMapsPage() {
    console.log('🔍 开始分析Google Maps页面结构...');
    
    // 1. 查找所有可能的商家容器
    const possibleContainers = [
        '[data-result-index]',
        'div[role="article"]', 
        'div[jsaction*="mouseover"]',
        'div[data-cid]',
        '.Nv2PK',
        '.bfdHYd',
        '.lI9IFe',
        '.VkpGBb'
    ];
    
    console.log('📦 分析可能的商家容器...');
    possibleContainers.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`${selector}: ${elements.length} 个元素`);
        
        if (elements.length > 0) {
            console.log(`  示例元素:`, elements[0]);
            console.log(`  示例文本:`, elements[0].textContent.substring(0, 100));
        }
    });
    
    // 2. 分析搜索结果区域
    console.log('\n📍 分析搜索结果区域...');
    const resultAreas = [
        '[role="main"]',
        '#pane',
        '.m6QErb',
        '[aria-label*="results"]'
    ];
    
    resultAreas.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            console.log(`${selector}: 找到`);
            console.log(`  子元素数量: ${element.children.length}`);
            console.log(`  滚动高度: ${element.scrollHeight}`);
        } else {
            console.log(`${selector}: 未找到`);
        }
    });
    
    // 3. 分析文本内容模式
    console.log('\n📝 分析文本内容模式...');
    const allDivs = document.querySelectorAll('div');
    const textPatterns = new Map();
    
    Array.from(allDivs).forEach(div => {
        const text = div.textContent.trim();
        if (text.length > 5 && text.length < 100) {
            // 检查是否包含商家信息的模式
            if (text.includes('★') || text.includes('·') || text.match(/\d+\.?\d*\s*km/)) {
                const pattern = text.replace(/\d+/g, 'N').replace(/[^\w\s★·]/g, '');
                textPatterns.set(pattern, (textPatterns.get(pattern) || 0) + 1);
            }
        }
    });
    
    console.log('常见文本模式:');
    Array.from(textPatterns.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .forEach(([pattern, count]) => {
            console.log(`  "${pattern}": ${count} 次`);
        });
    
    // 4. 查找搜索框
    console.log('\n🔍 分析搜索框...');
    const searchSelectors = [
        'input[data-value="Search"]',
        '#searchboxinput',
        'input[aria-label*="Search"]',
        'input[placeholder*="搜索"]'
    ];
    
    searchSelectors.forEach(selector => {
        const element = document.querySelector(selector);
        console.log(`${selector}: ${element ? '找到' : '未找到'}`);
        if (element) {
            console.log(`  当前值: "${element.value}"`);
            console.log(`  placeholder: "${element.placeholder}"`);
        }
    });
    
    // 5. 生成建议的选择器
    console.log('\n💡 生成选择器建议...');
    
    // 查找包含商家信息的元素
    const businessElements = Array.from(document.querySelectorAll('div')).filter(div => {
        const text = div.textContent.trim();
        return text.includes('★') && text.length > 10 && text.length < 200;
    });
    
    if (businessElements.length > 0) {
        console.log(`找到 ${businessElements.length} 个可能的商家元素`);
        
        // 分析这些元素的共同特征
        const commonClasses = new Map();
        const commonAttributes = new Map();
        
        businessElements.forEach(el => {
            // 分析class
            if (el.className) {
                el.className.split(' ').forEach(cls => {
                    if (cls.trim()) {
                        commonClasses.set(cls, (commonClasses.get(cls) || 0) + 1);
                    }
                });
            }
            
            // 分析属性
            Array.from(el.attributes).forEach(attr => {
                if (attr.name !== 'class') {
                    commonAttributes.set(attr.name, (commonAttributes.get(attr.name) || 0) + 1);
                }
            });
        });
        
        console.log('常见class名称:');
        Array.from(commonClasses.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([cls, count]) => {
                console.log(`  .${cls}: ${count} 次`);
            });
            
        console.log('常见属性:');
        Array.from(commonAttributes.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([attr, count]) => {
                console.log(`  [${attr}]: ${count} 次`);
            });
    }
    
    console.log('\n✅ 页面分析完成！');
}

// 运行分析
analyzeGoogleMapsPage();

// 导出函数供外部使用
window.analyzeGoogleMapsPage = analyzeGoogleMapsPage;
