// content.js - Google地图页面数据采集脚本
class GoogleMapsCollector {
  constructor() {
    this.isCollecting = false;
    this.collectedPlaces = new Set();
    this.searchKeyword = '';
    this.searchLocation = '';
    this.collectionInterval = null;
    this.scrollAttempts = 0;
    this.maxScrollAttempts = 50;
    this.init();
  }

  init() {
    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
    
    console.log('LBS数据采集器已加载');
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'startCollection':
        this.startCollection(message.keyword, message.location);
        break;
      case 'stopCollection':
        this.stopCollection();
        break;
    }
  }

  async startCollection(keyword, location = '') {
    if (this.isCollecting) {
      return;
    }

    this.isCollecting = true;
    this.searchKeyword = keyword;
    this.searchLocation = location;
    this.collectedPlaces.clear();
    this.scrollAttempts = 0;

    console.log(`开始采集: ${keyword} ${location}`);

    try {
      // 首先执行搜索
      await this.performSearch(keyword, location);
      
      // 等待搜索结果加载
      await this.waitForSearchResults();
      
      // 开始采集数据
      this.startDataCollection();
      
    } catch (error) {
      console.error('采集启动失败:', error);
      this.sendMessage('collectionError', { error: error.message });
      this.isCollecting = false;
    }
  }

  async performSearch(keyword, location) {
    const searchQuery = location ? `${keyword} ${location}` : keyword;
    
    // 查找搜索框
    const searchBox = document.querySelector('input[data-value="Search"]') || 
                     document.querySelector('#searchboxinput') ||
                     document.querySelector('input[aria-label*="Search"]');
    
    if (!searchBox) {
      throw new Error('未找到搜索框，请确保在Google地图页面');
    }

    // 清空并输入搜索内容
    searchBox.focus();
    searchBox.value = '';
    searchBox.dispatchEvent(new Event('input', { bubbles: true }));
    
    // 模拟输入
    for (let char of searchQuery) {
      searchBox.value += char;
      searchBox.dispatchEvent(new Event('input', { bubbles: true }));
      await this.sleep(50);
    }

    // 提交搜索
    searchBox.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));
    
    console.log(`搜索: ${searchQuery}`);
  }

  async waitForSearchResults() {
    console.log('等待搜索结果加载...');
    
    for (let i = 0; i < 30; i++) {
      const results = this.getPlaceElements();
      if (results.length > 0) {
        console.log(`找到 ${results.length} 个初始结果`);
        return;
      }
      await this.sleep(1000);
    }
    
    throw new Error('搜索结果加载超时');
  }

  startDataCollection() {
    console.log('开始数据采集...');
    
    this.collectionInterval = setInterval(() => {
      if (!this.isCollecting) {
        return;
      }
      
      this.collectCurrentPageData();
      this.scrollToLoadMore();
      
    }, 2000);
  }

  collectCurrentPageData() {
    const placeElements = this.getPlaceElements();
    
    placeElements.forEach(element => {
      try {
        const placeData = this.extractPlaceData(element);
        if (placeData && placeData.name) {
          const placeKey = `${placeData.name}-${placeData.address}`;
          if (!this.collectedPlaces.has(placeKey)) {
            this.collectedPlaces.add(placeKey);
            this.sendMessage('dataCollected', placeData);
            console.log('采集到:', placeData.name);
          }
        }
      } catch (error) {
        console.error('提取数据失败:', error);
      }
    });
  }

  getPlaceElements() {
    // 多种可能的选择器，适应不同的Google地图布局
    const selectors = [
      '[data-result-index]',
      '[role="article"]',
      '.Nv2PK',
      '[jsaction*="mouseover"]',
      '.bfdHYd',
      '[data-cid]'
    ];
    
    for (let selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        return Array.from(elements);
      }
    }
    
    return [];
  }

  extractPlaceData(element) {
    const data = {
      name: '',
      address: '',
      phone: '',
      rating: '',
      reviewCount: '',
      type: '',
      website: '',
      hours: '',
      timestamp: new Date().toISOString()
    };

    try {
      // 提取商家名称 - 更全面的选择器
      const nameSelectors = [
        '.qBF1Pd',
        '.DUwDvf',
        '.fontHeadlineSmall',
        '[data-value="Name"]',
        'h3',
        '.section-result-title',
        '.section-result-title span',
        'a[data-value="title"] span'
      ];

      for (let selector of nameSelectors) {
        const nameEl = element.querySelector(selector);
        if (nameEl && nameEl.textContent.trim()) {
          data.name = nameEl.textContent.trim();
          break;
        }
      }

      // 提取地址 - 改进的地址识别
      const addressSelectors = [
        '.W4Efsd:last-child',
        '.W4Efsd[span]',
        '[data-value="Address"]',
        '.rogA2c',
        '.section-result-location',
        '.section-result-details .fontBodyMedium'
      ];

      for (let selector of addressSelectors) {
        const addressEl = element.querySelector(selector);
        if (addressEl && addressEl.textContent.trim()) {
          const addressText = addressEl.textContent.trim();
          // 过滤掉明显不是地址的内容
          if (!addressText.match(/^\d+\.?\d*$/) && !addressText.includes('★') && addressText.length > 5) {
            data.address = addressText;
            break;
          }
        }
      }

      // 提取评分 - 更精确的评分匹配
      const ratingSelectors = [
        '.MW4etd',
        '[data-value="Rating"]',
        '.fontBodyMedium span',
        '.section-result-rating span'
      ];

      for (let selector of ratingSelectors) {
        const ratingEl = element.querySelector(selector);
        if (ratingEl) {
          const ratingMatch = ratingEl.textContent.match(/(\d+\.?\d*)/);
          if (ratingMatch && parseFloat(ratingMatch[1]) <= 5) {
            data.rating = ratingMatch[1];
            break;
          }
        }
      }

      // 提取评论数 - 改进的评论数识别
      const reviewSelectors = [
        '.UY7F9',
        '[data-value="Reviews"]',
        '.section-result-num-reviews'
      ];

      for (let selector of reviewSelectors) {
        const reviewEl = element.querySelector(selector);
        if (reviewEl) {
          const reviewText = reviewEl.textContent.trim();
          const reviewMatch = reviewText.match(/\(([^)]+)\)/);
          if (reviewMatch) {
            data.reviewCount = reviewMatch[1];
            break;
          }
        }
      }

      // 提取商家类型 - 更智能的类型识别
      const typeSelectors = [
        '.W4Efsd:first-child',
        '[data-value="Category"]',
        '.section-result-details .fontBodyMedium:first-child'
      ];

      for (let selector of typeSelectors) {
        const typeEl = element.querySelector(selector);
        if (typeEl && typeEl.textContent.trim()) {
          const typeText = typeEl.textContent.trim();
          // 过滤掉地址、评分等非类型信息
          if (!typeText.includes('·') && !typeText.match(/\d+\.?\d*/) &&
              !typeText.includes('★') && typeText.length < 50) {
            data.type = typeText;
            break;
          }
        }
      }

      // 异步获取详细信息
      setTimeout(() => this.clickForDetails(element, data), 500);

    } catch (error) {
      console.error('数据提取错误:', error);
    }

    return data;
  }

  async clickForDetails(element, data) {
    try {
      // 保存当前滚动位置
      const resultsContainer = document.querySelector('[role="main"]') ||
                              document.querySelector('.m6QErb') ||
                              document.querySelector('#pane');
      const scrollTop = resultsContainer ? resultsContainer.scrollTop : 0;

      // 模拟点击获取更多详细信息
      element.click();
      await this.sleep(1500);

      // 尝试获取电话号码
      const phoneSelectors = [
        '[data-item-id*="phone"]',
        '[data-value="Phone number"]',
        'button[data-value*="phone"]',
        '[aria-label*="phone"]',
        '[aria-label*="电话"]'
      ];

      for (let selector of phoneSelectors) {
        const phoneEl = document.querySelector(selector);
        if (phoneEl && phoneEl.textContent.trim()) {
          const phoneText = phoneEl.textContent.trim();
          if (phoneText.match(/[\d\+\-\(\)\s]/)) {
            data.phone = phoneText;
            break;
          }
        }
      }

      // 尝试获取网站
      const websiteSelectors = [
        '[data-item-id*="authority"]',
        '[data-value="Website"]',
        'a[href*="http"]',
        '[aria-label*="website"]',
        '[aria-label*="网站"]'
      ];

      for (let selector of websiteSelectors) {
        const websiteEl = document.querySelector(selector);
        if (websiteEl) {
          const website = websiteEl.href || websiteEl.textContent.trim();
          if (website.startsWith('http')) {
            data.website = website;
            break;
          }
        }
      }

      // 尝试获取营业时间
      const hoursSelectors = [
        '[data-item-id*="oh"]',
        '[data-value="Hours"]',
        '[aria-label*="hours"]',
        '[aria-label*="营业时间"]'
      ];

      for (let selector of hoursSelectors) {
        const hoursEl = document.querySelector(selector);
        if (hoursEl && hoursEl.textContent.trim()) {
          data.hours = hoursEl.textContent.trim();
          break;
        }
      }

      // 恢复滚动位置
      if (resultsContainer) {
        resultsContainer.scrollTop = scrollTop;
      }

    } catch (error) {
      console.error('获取详细信息失败:', error);
    }
  }

  scrollToLoadMore() {
    const resultsContainer = document.querySelector('[role="main"]') ||
                           document.querySelector('.m6QErb') ||
                           document.querySelector('#pane');
    
    if (resultsContainer) {
      resultsContainer.scrollTop = resultsContainer.scrollHeight;
      this.scrollAttempts++;
      
      if (this.scrollAttempts >= this.maxScrollAttempts) {
        console.log('达到最大滚动次数，采集完成');
        this.stopCollection();
        this.sendMessage('collectionComplete');
      }
    }
  }

  stopCollection() {
    this.isCollecting = false;
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }
    console.log('数据采集已停止');
  }

  sendMessage(action, data = {}) {
    chrome.runtime.sendMessage({
      action: action,
      data: data
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 初始化采集器
const collector = new GoogleMapsCollector();
