// content.js - Google地图页面数据采集脚本（悬浮UI版本）
class GoogleMapsCollector {
  constructor() {
    this.isCollecting = false;
    this.collectedPlaces = new Set();
    this.collectedData = [];
    this.searchKeyword = '';
    this.searchLocation = '';
    this.collectionInterval = null;
    this.scrollAttempts = 0;
    this.maxScrollAttempts = 50;

    // UI相关属性
    this.floatingUI = null;
    this.isExpanded = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };

    this.init();
  }

  init() {
    // 等待页面加载完成后创建UI
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.createFloatingUI());
    } else {
      this.createFloatingUI();
    }

    // 加载存储的数据
    this.loadStoredData();

    console.log('LBS数据采集器已加载（悬浮UI模式）');
  }

  createFloatingUI() {
    // 创建悬浮容器
    this.floatingUI = document.createElement('div');
    this.floatingUI.id = 'lbs-collector-floating-ui';
    this.floatingUI.innerHTML = this.getUIHTML();

    // 添加样式
    this.addUIStyles();

    // 添加到页面
    document.body.appendChild(this.floatingUI);

    // 绑定事件
    this.bindUIEvents();

    // 初始化UI状态
    this.updateUI();
  }

  getUIHTML() {
    return `
      <!-- 悬浮按钮 -->
      <div class="lbs-floating-button" id="lbs-floating-btn">
        <div class="lbs-btn-icon">🗺️</div>
        <div class="lbs-btn-text">LBS采集</div>
      </div>

      <!-- 主面板 -->
      <div class="lbs-main-panel" id="lbs-main-panel">
        <div class="lbs-panel-header">
          <div class="lbs-panel-title">
            <span class="lbs-title-icon">🗺️</span>
            <span class="lbs-title-text">LBS数据采集器</span>
          </div>
          <div class="lbs-panel-controls">
            <button class="lbs-control-btn" id="lbs-minimize-btn" title="最小化">−</button>
            <button class="lbs-control-btn" id="lbs-close-btn" title="关闭">×</button>
          </div>
        </div>

        <div class="lbs-panel-content">
          <div class="lbs-search-section">
            <div class="lbs-input-group">
              <label for="lbs-keyword">搜索关键词:</label>
              <input type="text" id="lbs-keyword" placeholder="例如: 餐厅, 咖啡店, 超市" />
            </div>

            <div class="lbs-input-group">
              <label for="lbs-location">地区 (可选):</label>
              <input type="text" id="lbs-location" placeholder="例如: 北京, 上海" />
            </div>
          </div>

          <div class="lbs-button-group">
            <button id="lbs-start-btn" class="lbs-btn lbs-btn-primary">开始采集</button>
            <button id="lbs-stop-btn" class="lbs-btn lbs-btn-secondary" disabled>停止采集</button>
          </div>

          <div id="lbs-status" class="lbs-status"></div>

          <div id="lbs-data-summary" class="lbs-data-summary">
            <div id="lbs-data-count" class="lbs-data-count">已采集: 0 条数据</div>
            <div class="lbs-progress-bar">
              <div id="lbs-progress-fill" class="lbs-progress-fill"></div>
            </div>
            <div id="lbs-data-preview" class="lbs-data-preview"></div>
          </div>

          <div class="lbs-button-group">
            <button id="lbs-export-btn" class="lbs-btn lbs-btn-success" disabled>导出CSV</button>
            <button id="lbs-clear-btn" class="lbs-btn lbs-btn-secondary">清空数据</button>
          </div>

          <div id="lbs-debug-panel" class="lbs-debug-panel" style="display: none;">
            <div class="lbs-debug-title">调试信息</div>
            <div id="lbs-debug-content" class="lbs-debug-content"></div>
            <div style="display: flex; gap: 5px; margin-top: 8px;">
              <button id="lbs-toggle-debug" class="lbs-btn lbs-btn-secondary" style="font-size: 12px; padding: 5px; flex: 1;">
                显示调试
              </button>
              <button id="lbs-test-extraction" class="lbs-btn lbs-btn-secondary" style="font-size: 12px; padding: 5px; flex: 1;">
                测试提取
              </button>
              <button id="lbs-analyze-page" class="lbs-btn lbs-btn-secondary" style="font-size: 12px; padding: 5px; flex: 1;">
                分析页面
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  addUIStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* 悬浮UI基础样式 */
      #lbs-collector-floating-ui {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 999999;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 悬浮按钮样式 */
      .lbs-floating-button {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #1a73e8, #1557b0);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
        transition: all 0.3s ease;
        user-select: none;
      }

      .lbs-floating-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(26, 115, 232, 0.4);
      }

      .lbs-btn-icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      .lbs-btn-text {
        font-size: 8px;
        color: white;
        font-weight: 500;
      }

      /* 主面板样式 */
      .lbs-main-panel {
        width: 350px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;
        display: none;
        overflow: hidden;
        animation: slideIn 0.3s ease-out;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      /* 面板头部 */
      .lbs-panel-header {
        background: linear-gradient(135deg, #1a73e8, #1557b0);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
      }

      .lbs-panel-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }

      .lbs-title-icon {
        font-size: 16px;
      }

      .lbs-panel-controls {
        display: flex;
        gap: 4px;
      }

      .lbs-control-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        transition: background 0.2s;
      }

      .lbs-control-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      /* 面板内容 */
      .lbs-panel-content {
        padding: 20px;
      }

      .lbs-search-section {
        margin-bottom: 20px;
      }

      .lbs-input-group {
        margin-bottom: 15px;
      }

      .lbs-input-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
      }

      .lbs-input-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .lbs-input-group input:focus {
        outline: none;
        border-color: #1a73e8;
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
      }

      /* 按钮样式 */
      .lbs-button-group {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .lbs-btn {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        user-select: none;
      }

      .lbs-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .lbs-btn-primary {
        background: #1a73e8;
        color: white;
      }

      .lbs-btn-primary:hover:not(:disabled) {
        background: #1557b0;
        transform: translateY(-1px);
      }

      .lbs-btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #ddd;
      }

      .lbs-btn-secondary:hover:not(:disabled) {
        background: #e8f0fe;
        border-color: #1a73e8;
      }

      .lbs-btn-success {
        background: #34a853;
        color: white;
      }

      .lbs-btn-success:hover:not(:disabled) {
        background: #2d8f47;
        transform: translateY(-1px);
      }

      /* 状态显示 */
      .lbs-status {
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        font-size: 14px;
        display: none;
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      .lbs-status.info {
        background: #e8f0fe;
        color: #1a73e8;
        border: 1px solid #dadce0;
      }

      .lbs-status.success {
        background: #e6f4ea;
        color: #137333;
        border: 1px solid #34a853;
      }

      .lbs-status.error {
        background: #fce8e6;
        color: #d93025;
        border: 1px solid #ea4335;
      }

      /* 数据摘要 */
      .lbs-data-summary {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        display: none;
      }

      .lbs-data-count {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
      }

      .lbs-progress-bar {
        width: 100%;
        height: 6px;
        background: #e0e0e0;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 10px;
      }

      .lbs-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #1a73e8, #34a853);
        width: 0%;
        transition: width 0.3s ease;
      }

      .lbs-data-preview {
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 10px;
        font-size: 12px;
        background: white;
      }

      .lbs-data-item {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .lbs-data-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .lbs-business-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
      }

      .lbs-business-details {
        color: #666;
        font-size: 11px;
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        #lbs-collector-floating-ui {
          right: 10px;
          top: 10px;
        }

        .lbs-main-panel {
          width: calc(100vw - 20px);
          max-width: 350px;
        }
      }

      /* 拖拽状态 */
      .lbs-dragging {
        cursor: move !important;
        user-select: none;
      }

      .lbs-dragging .lbs-main-panel {
        transform: rotate(2deg);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
      }

      /* 调试面板 */
      .lbs-debug-panel {
        margin-top: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e0e0e0;
      }

      .lbs-debug-title {
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
        font-size: 13px;
      }

      .lbs-debug-content {
        font-family: monospace;
        font-size: 11px;
        background: white;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
        max-height: 100px;
        overflow-y: auto;
        white-space: pre-wrap;
        color: #666;
      }
    `;

    document.head.appendChild(style);
  }

  bindUIEvents() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');
    const minimizeBtn = document.getElementById('lbs-minimize-btn');
    const closeBtn = document.getElementById('lbs-close-btn');
    const startBtn = document.getElementById('lbs-start-btn');
    const stopBtn = document.getElementById('lbs-stop-btn');
    const exportBtn = document.getElementById('lbs-export-btn');
    const clearBtn = document.getElementById('lbs-clear-btn');
    const toggleDebugBtn = document.getElementById('lbs-toggle-debug');
    const testExtractionBtn = document.getElementById('lbs-test-extraction');
    const analyzePageBtn = document.getElementById('lbs-analyze-page');
    const panelHeader = document.querySelector('.lbs-panel-header');

    // 悬浮按钮点击事件
    floatingBtn.addEventListener('click', () => this.togglePanel());

    // 最小化按钮
    minimizeBtn.addEventListener('click', () => this.minimizePanel());

    // 关闭按钮
    closeBtn.addEventListener('click', () => this.closePanel());

    // 功能按钮
    startBtn.addEventListener('click', () => {
      console.log('🎯 [LBS采集器] 用户点击开始采集按钮');
      this.startCollection();
    });
    stopBtn.addEventListener('click', () => {
      console.log('🛑 [LBS采集器] 用户点击停止采集按钮');
      this.stopCollection();
    });
    exportBtn.addEventListener('click', () => {
      console.log('📁 [LBS采集器] 用户点击导出CSV按钮');
      this.exportCsv();
    });
    clearBtn.addEventListener('click', () => {
      console.log('🗑️ [LBS采集器] 用户点击清空数据按钮');
      this.clearData();
    });

    // 调试面板切换
    toggleDebugBtn.addEventListener('click', () => {
      console.log('🔧 [LBS采集器] 用户切换调试面板');
      this.toggleDebugPanel();
    });

    // 测试数据提取
    testExtractionBtn.addEventListener('click', () => {
      console.log('🧪 [LBS采集器] 用户点击测试提取');
      this.testDataExtraction();
    });

    // 分析页面结构
    analyzePageBtn.addEventListener('click', () => {
      console.log('🔍 [LBS采集器] 用户点击分析页面');
      this.analyzePageStructure();
    });

    // 拖拽功能
    this.setupDragFunctionality(panelHeader);

    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));

    // 点击外部关闭面板
    document.addEventListener('click', (e) => {
      if (this.isExpanded && !this.floatingUI.contains(e.target)) {
        this.minimizePanel();
      }
    });
  }

  setupDragFunctionality(dragHandle) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    dragHandle.addEventListener('mousedown', (e) => {
      isDragging = true;
      this.isDragging = true;

      const rect = this.floatingUI.getBoundingClientRect();
      startX = e.clientX;
      startY = e.clientY;
      startLeft = rect.left;
      startTop = rect.top;

      this.floatingUI.classList.add('lbs-dragging');

      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newLeft = startLeft + deltaX;
      let newTop = startTop + deltaY;

      // 边界检查
      const maxLeft = window.innerWidth - this.floatingUI.offsetWidth;
      const maxTop = window.innerHeight - this.floatingUI.offsetHeight;

      newLeft = Math.max(0, Math.min(newLeft, maxLeft));
      newTop = Math.max(0, Math.min(newTop, maxTop));

      this.floatingUI.style.left = newLeft + 'px';
      this.floatingUI.style.top = newTop + 'px';
      this.floatingUI.style.right = 'auto';
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        this.isDragging = false;
        this.floatingUI.classList.remove('lbs-dragging');
      }
    });
  }

  handleKeyboard(e) {
    // Ctrl + Shift + L 切换面板
    if (e.ctrlKey && e.shiftKey && e.key === 'L') {
      e.preventDefault();
      this.togglePanel();
    }

    // ESC 关闭面板
    if (e.key === 'Escape' && this.isExpanded) {
      this.minimizePanel();
    }
  }

  togglePanel() {
    if (this.isExpanded) {
      this.minimizePanel();
    } else {
      this.expandPanel();
    }
  }

  expandPanel() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');

    floatingBtn.style.display = 'none';
    mainPanel.style.display = 'block';
    this.isExpanded = true;

    // 更新UI状态
    this.updateUI();
  }

  minimizePanel() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');

    mainPanel.style.display = 'none';
    floatingBtn.style.display = 'flex';
    this.isExpanded = false;
  }

  closePanel() {
    this.floatingUI.style.display = 'none';
  }

  // 数据管理方法
  async loadStoredData() {
    try {
      const result = await chrome.storage.local.get(['lbsData', 'isCollecting']);
      this.collectedData = result.lbsData || [];
      this.isCollecting = result.isCollecting || false;

      // 如果UI已创建，更新显示
      if (this.floatingUI) {
        this.updateUI();
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  }

  async saveData() {
    try {
      await chrome.storage.local.set({
        lbsData: this.collectedData,
        isCollecting: this.isCollecting
      });
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  addCollectedData(newData) {
    // 更严格的重复数据检查
    const exists = this.collectedData.some(item => {
      const nameMatch = item.name === newData.name;
      const addressMatch = item.address === newData.address;
      const phoneMatch = item.phone && newData.phone && item.phone === newData.phone;

      return nameMatch && (addressMatch || phoneMatch);
    });

    if (!exists && newData.name && newData.name.trim()) {
      // 数据清理和标准化
      const cleanedData = this.cleanData(newData);
      this.collectedData.push(cleanedData);
      this.saveData();
      this.updateUI();
      this.showStatus(`已采集 ${this.collectedData.length} 条数据`, 'info');
    }
  }

  cleanData(data) {
    return {
      name: this.cleanText(data.name),
      address: this.cleanText(data.address),
      phone: this.cleanPhone(data.phone),
      rating: this.cleanRating(data.rating),
      reviewCount: this.cleanReviewCount(data.reviewCount),
      type: this.cleanText(data.type),
      website: this.cleanUrl(data.website),
      hours: this.cleanText(data.hours),
      timestamp: data.timestamp || new Date().toISOString()
    };
  }

  cleanText(text) {
    if (!text) return '';
    return text.trim().replace(/\s+/g, ' ');
  }

  cleanPhone(phone) {
    if (!phone) return '';
    return phone.replace(/[^\d\+\-\(\)\s]/g, '').trim();
  }

  cleanRating(rating) {
    if (!rating) return '';
    const match = rating.match(/(\d+\.?\d*)/);
    return match ? match[1] : '';
  }

  cleanReviewCount(reviewCount) {
    if (!reviewCount) return '';
    const match = reviewCount.match(/(\d+)/);
    return match ? match[1] : '';
  }

  cleanUrl(url) {
    if (!url) return '';
    try {
      new URL(url);
      return url;
    } catch {
      return url.startsWith('http') ? url : '';
    }
  }

  updateUI() {
    if (!this.floatingUI) return;

    const startBtn = document.getElementById('lbs-start-btn');
    const stopBtn = document.getElementById('lbs-stop-btn');
    const exportBtn = document.getElementById('lbs-export-btn');
    const dataSummary = document.getElementById('lbs-data-summary');
    const dataCount = document.getElementById('lbs-data-count');
    const dataPreview = document.getElementById('lbs-data-preview');
    const progressFill = document.getElementById('lbs-progress-fill');
    const floatingBtn = document.getElementById('lbs-floating-btn');

    // 更新按钮状态
    if (startBtn && stopBtn && exportBtn) {
      startBtn.disabled = this.isCollecting;
      stopBtn.disabled = !this.isCollecting;
      exportBtn.disabled = this.collectedData.length === 0;

      // 更新按钮文本
      if (this.isCollecting) {
        startBtn.textContent = '采集中...';
        stopBtn.textContent = '停止采集';
      } else {
        startBtn.textContent = '开始采集';
        stopBtn.textContent = '停止采集';
      }

      // 更新导出按钮文本
      if (this.collectedData.length > 0) {
        exportBtn.textContent = `导出CSV (${this.collectedData.length}条)`;
      } else {
        exportBtn.textContent = '导出CSV';
      }
    }

    // 更新悬浮按钮状态
    if (floatingBtn) {
      const btnText = floatingBtn.querySelector('.lbs-btn-text');
      if (this.isCollecting) {
        btnText.textContent = '采集中';
        floatingBtn.style.background = 'linear-gradient(135deg, #34a853, #2d8f47)';
      } else if (this.collectedData.length > 0) {
        btnText.textContent = `${this.collectedData.length}条数据`;
        floatingBtn.style.background = 'linear-gradient(135deg, #1a73e8, #1557b0)';
      } else {
        btnText.textContent = 'LBS采集';
        floatingBtn.style.background = 'linear-gradient(135deg, #1a73e8, #1557b0)';
      }
    }

    // 更新数据摘要
    if (dataSummary && dataCount && dataPreview && progressFill) {
      if (this.collectedData.length > 0) {
        dataSummary.style.display = 'block';

        // 数据统计
        const stats = this.generateDataStats();
        dataCount.innerHTML = `
          <div>已采集: <strong>${this.collectedData.length}</strong> 条数据</div>
          <div style="font-size: 12px; color: #666; margin-top: 5px;">
            📞 ${stats.withPhone}个有电话 | ⭐ ${stats.withRating}个有评分 | 🌐 ${stats.withWebsite}个有网站
          </div>
        `;

        // 进度条
        const progress = Math.min((this.collectedData.length / 50) * 100, 100);
        progressFill.style.width = progress + '%';

        // 数据预览
        const recentData = this.collectedData.slice(-5).reverse();
        dataPreview.innerHTML = recentData.map((item) => {
          const completeness = this.calculateCompleteness(item);
          const completenessColor = completeness >= 80 ? '#34a853' : completeness >= 60 ? '#fbbc04' : '#ea4335';

          return `
            <div class="lbs-data-item">
              <div class="lbs-business-name">
                ${item.name || '未知商家'}
                <span style="float: right; font-size: 10px; color: ${completenessColor};">
                  ${completeness}%
                </span>
              </div>
              <div class="lbs-business-details">
                📍 ${this.truncateText(item.address || '地址未知', 30)}<br>
                📞 ${item.phone || '电话未知'}<br>
                ⭐ ${item.rating || '暂无评分'} ${item.reviewCount ? `(${item.reviewCount}条评论)` : ''}
                ${item.type ? `<br>🏷️ ${item.type}` : ''}
              </div>
            </div>
          `;
        }).join('');
      } else {
        dataSummary.style.display = 'none';
      }
    }

    // 更新调试信息
    this.updateDebugInfo();
  }

  generateDataStats() {
    const total = this.collectedData.length;
    const withPhone = this.collectedData.filter(item => item.phone && item.phone.trim()).length;
    const withRating = this.collectedData.filter(item => item.rating && item.rating.trim()).length;
    const withWebsite = this.collectedData.filter(item => item.website && item.website.trim()).length;

    const ratings = this.collectedData
      .filter(item => item.rating && !isNaN(parseFloat(item.rating)))
      .map(item => parseFloat(item.rating));

    const avgRating = ratings.length > 0
      ? (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(2)
      : '无';

    return {
      total,
      withPhone,
      withRating,
      withWebsite,
      avgRating
    };
  }

  calculateCompleteness(item) {
    const fields = ['name', 'address', 'phone', 'rating', 'type'];
    const filledFields = fields.filter(field => item[field] && item[field].trim()).length;
    return Math.round((filledFields / fields.length) * 100);
  }

  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  showStatus(message, type) {
    const statusEl = document.getElementById('lbs-status');
    if (!statusEl) return;

    statusEl.textContent = message;
    statusEl.className = `lbs-status ${type}`;
    statusEl.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
      statusEl.style.display = 'none';
    }, 3000);
  }

  async exportCsv() {
    if (this.collectedData.length === 0) {
      this.showStatus('没有数据可导出', 'error');
      return;
    }

    try {
      const csvContent = this.generateCsv();
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `lbs_data_${timestamp}.csv`;

      // 创建下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showStatus(`已导出 ${this.collectedData.length} 条数据`, 'success');
    } catch (error) {
      console.error('导出失败:', error);
      this.showStatus('导出失败，请重试', 'error');
    }
  }

  generateCsv() {
    const headers = [
      '商家名称',
      '地址',
      '电话',
      '评分',
      '评论数',
      '商家类型',
      '网站',
      '营业时间',
      '采集时间',
      '数据完整度'
    ];
    const csvRows = [headers.join(',')];

    // 添加数据统计行
    const stats = this.generateDataStats();
    csvRows.push(''); // 空行
    csvRows.push('数据统计信息');
    csvRows.push(`总数据量,${stats.total}`);
    csvRows.push(`有电话号码,${stats.withPhone}`);
    csvRows.push(`有评分,${stats.withRating}`);
    csvRows.push(`有网站,${stats.withWebsite}`);
    csvRows.push(`平均评分,${stats.avgRating}`);
    csvRows.push(''); // 空行
    csvRows.push('详细数据');
    csvRows.push(headers.join(','));

    this.collectedData.forEach(item => {
      const completeness = this.calculateCompleteness(item);
      const row = [
        this.escapeCsvField(item.name || ''),
        this.escapeCsvField(item.address || ''),
        this.escapeCsvField(item.phone || ''),
        this.escapeCsvField(item.rating || ''),
        this.escapeCsvField(item.reviewCount || ''),
        this.escapeCsvField(item.type || ''),
        this.escapeCsvField(item.website || ''),
        this.escapeCsvField(item.hours || ''),
        this.escapeCsvField(item.timestamp || ''),
        this.escapeCsvField(completeness + '%')
      ];
      csvRows.push(row.join(','));
    });

    return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
  }

  escapeCsvField(field) {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  async clearData() {
    if (confirm('确定要清空所有采集的数据吗？')) {
      this.collectedData = [];
      await this.saveData();
      this.updateUI();
      this.showStatus('数据已清空', 'info');
    }
  }

  // 采集功能方法
  async startCollection() {
    console.log('🚀 [LBS采集器] 开始启动采集流程...');

    const keywordInput = document.getElementById('lbs-keyword');
    const locationInput = document.getElementById('lbs-location');

    const keyword = keywordInput ? keywordInput.value.trim() : '';
    const location = locationInput ? locationInput.value.trim() : '';

    console.log(`📝 [LBS采集器] 输入参数 - 关键词: "${keyword}", 地区: "${location}"`);

    if (!keyword) {
      console.warn('⚠️ [LBS采集器] 关键词为空，停止采集');
      this.showStatus('请输入搜索关键词', 'error');
      return;
    }

    console.log('🔄 [LBS采集器] 设置采集状态...');
    this.isCollecting = true;
    this.searchKeyword = keyword;
    this.searchLocation = location;
    this.collectedPlaces.clear();
    this.scrollAttempts = 0;

    await this.saveData();
    this.updateUI();
    this.showStatus('开始采集数据...', 'info');

    console.log('✅ [LBS采集器] 采集状态已设置，当前状态:', {
      isCollecting: this.isCollecting,
      keyword: this.searchKeyword,
      location: this.searchLocation,
      collectedCount: this.collectedData.length
    });

    try {
      console.log('🔍 [LBS采集器] 步骤1: 执行搜索...');
      await this.performSearch(keyword, location);

      console.log('⏳ [LBS采集器] 步骤2: 等待搜索结果加载...');
      await this.waitForSearchResults();

      console.log('📊 [LBS采集器] 步骤3: 开始数据采集...');
      this.startDataCollection();

    } catch (error) {
      console.error('❌ [LBS采集器] 采集启动失败:', error);
      this.showStatus(`采集失败: ${error.message}`, 'error');
      this.isCollecting = false;
      await this.saveData();
      this.updateUI();
      console.log('🔄 [LBS采集器] 已重置采集状态');
    }
  }

  async performSearch(keyword, location) {
    const searchQuery = location ? `${keyword} ${location}` : keyword;
    console.log(`🔍 [LBS采集器] 准备搜索: "${searchQuery}"`);

    // 查找搜索框
    console.log('🔍 [LBS采集器] 查找搜索框...');
    const searchBox = document.querySelector('input[data-value="Search"]') ||
                     document.querySelector('#searchboxinput') ||
                     document.querySelector('input[aria-label*="Search"]') ||
                     document.querySelector('input[placeholder*="搜索"]');

    if (!searchBox) {
      console.error('❌ [LBS采集器] 未找到搜索框');
      throw new Error('未找到搜索框，请确保在Google地图页面');
    }

    console.log('✅ [LBS采集器] 找到搜索框:', searchBox);

    // 清空并输入搜索内容
    console.log('📝 [LBS采集器] 清空搜索框并输入内容...');
    searchBox.focus();
    searchBox.value = '';
    searchBox.dispatchEvent(new Event('input', { bubbles: true }));

    // 模拟输入
    console.log('⌨️ [LBS采集器] 模拟键盘输入...');
    for (let char of searchQuery) {
      if (!this.isCollecting) {
        console.log('🛑 [LBS采集器] 采集已停止，中断搜索输入');
        return;
      }
      searchBox.value += char;
      searchBox.dispatchEvent(new Event('input', { bubbles: true }));
      await this.sleep(50);
    }

    // 提交搜索
    console.log('🚀 [LBS采集器] 提交搜索请求...');
    searchBox.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));

    console.log(`✅ [LBS采集器] 搜索已提交: "${searchQuery}"`);
  }

  async waitForSearchResults() {
    console.log('⏳ [LBS采集器] 等待搜索结果加载...');

    for (let i = 0; i < 30; i++) {
      if (!this.isCollecting) {
        console.log('🛑 [LBS采集器] 采集已停止，中断等待搜索结果');
        return;
      }

      const results = this.getPlaceElements();
      console.log(`🔍 [LBS采集器] 第${i+1}次检查，找到 ${results.length} 个元素`);

      if (results.length > 0) {
        console.log(`✅ [LBS采集器] 搜索结果加载完成，找到 ${results.length} 个初始结果`);
        return;
      }
      await this.sleep(1000);
    }

    console.error('❌ [LBS采集器] 搜索结果加载超时');
    throw new Error('搜索结果加载超时');
  }

  startDataCollection() {
    console.log('📊 [LBS采集器] 开始数据采集循环...');

    // 清除之前的定时器（如果存在）
    if (this.collectionInterval) {
      console.log('🔄 [LBS采集器] 清除之前的采集定时器');
      clearInterval(this.collectionInterval);
    }

    this.collectionInterval = setInterval(() => {
      if (!this.isCollecting) {
        console.log('🛑 [LBS采集器] 检测到停止信号，清除采集定时器');
        clearInterval(this.collectionInterval);
        this.collectionInterval = null;
        return;
      }

      console.log(`🔄 [LBS采集器] 执行采集循环 - 滚动次数: ${this.scrollAttempts}/${this.maxScrollAttempts}`);
      this.collectCurrentPageData();
      this.scrollToLoadMore();

    }, 2000);

    console.log('✅ [LBS采集器] 采集定时器已启动，间隔2秒');
  }

  collectCurrentPageData() {
    const placeElements = this.getPlaceElements();

    placeElements.forEach(element => {
      try {
        const placeData = this.extractPlaceData(element);
        if (placeData && placeData.name) {
          const placeKey = `${placeData.name}-${placeData.address}`;
          if (!this.collectedPlaces.has(placeKey)) {
            this.collectedPlaces.add(placeKey);
            this.addCollectedData(placeData);
            console.log('采集到:', placeData.name);
          }
        }
      } catch (error) {
        console.error('提取数据失败:', error);
      }
    });
  }

  stopCollection() {
    console.log('🛑 [LBS采集器] 收到停止采集指令...');

    // 设置停止标志
    this.isCollecting = false;
    console.log('✅ [LBS采集器] 已设置停止标志: isCollecting = false');

    // 清除定时器
    if (this.collectionInterval) {
      console.log('🔄 [LBS采集器] 清除采集定时器...');
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
      console.log('✅ [LBS采集器] 定时器已清除');
    } else {
      console.log('ℹ️ [LBS采集器] 没有活动的定时器需要清除');
    }

    // 保存数据和更新UI
    console.log('💾 [LBS采集器] 保存数据并更新UI...');
    this.saveData();
    this.updateUI();

    const message = `采集完成！共采集到 ${this.collectedData.length} 条数据`;
    this.showStatus(message, 'success');
    console.log(`✅ [LBS采集器] ${message}`);

    // 输出采集统计
    console.log('📊 [LBS采集器] 采集统计:', {
      总数据量: this.collectedData.length,
      滚动次数: this.scrollAttempts,
      最大滚动次数: this.maxScrollAttempts,
      搜索关键词: this.searchKeyword,
      搜索地区: this.searchLocation
    });
  }

  // 增强交互功能
  setupAutoHide() {
    let hideTimer;

    const startHideTimer = () => {
      clearTimeout(hideTimer);
      hideTimer = setTimeout(() => {
        if (this.isExpanded && !this.isCollecting) {
          this.minimizePanel();
        }
      }, 30000); // 30秒后自动隐藏
    };

    const cancelHideTimer = () => {
      clearTimeout(hideTimer);
    };

    // 鼠标进入取消隐藏，离开开始计时
    this.floatingUI.addEventListener('mouseenter', cancelHideTimer);
    this.floatingUI.addEventListener('mouseleave', startHideTimer);

    // 初始启动计时器
    if (this.isExpanded) {
      startHideTimer();
    }
  }

  setupResizeObserver() {
    // 监听窗口大小变化，调整位置
    const resizeObserver = new ResizeObserver(() => {
      this.adjustPosition();
    });

    resizeObserver.observe(document.body);
  }

  adjustPosition() {
    if (!this.floatingUI) return;

    const rect = this.floatingUI.getBoundingClientRect();
    const maxLeft = window.innerWidth - rect.width;
    const maxTop = window.innerHeight - rect.height;

    let needsAdjustment = false;
    let newLeft = rect.left;
    let newTop = rect.top;

    if (rect.left > maxLeft) {
      newLeft = maxLeft;
      needsAdjustment = true;
    }

    if (rect.top > maxTop) {
      newTop = maxTop;
      needsAdjustment = true;
    }

    if (needsAdjustment) {
      this.floatingUI.style.left = Math.max(0, newLeft) + 'px';
      this.floatingUI.style.top = Math.max(0, newTop) + 'px';
      this.floatingUI.style.right = 'auto';
    }
  }

  // 添加快捷操作
  addQuickActions() {
    // 双击悬浮按钮快速开始采集
    const floatingBtn = document.getElementById('lbs-floating-btn');
    let clickCount = 0;

    floatingBtn.addEventListener('click', () => {
      clickCount++;
      setTimeout(() => {
        if (clickCount === 2) {
          // 双击事件
          this.quickStart();
        }
        clickCount = 0;
      }, 300);
    });
  }

  quickStart() {
    // 如果有上次的搜索关键词，直接开始采集
    if (this.searchKeyword) {
      this.expandPanel();
      setTimeout(() => {
        this.startCollection();
      }, 500);
    } else {
      this.expandPanel();
      // 聚焦到关键词输入框
      setTimeout(() => {
        const keywordInput = document.getElementById('lbs-keyword');
        if (keywordInput) {
          keywordInput.focus();
        }
      }, 300);
    }
  }

  // 添加右键菜单
  addContextMenu() {
    this.floatingUI.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showContextMenu(e.clientX, e.clientY);
    });
  }

  showContextMenu(x, y) {
    // 移除已存在的菜单
    const existingMenu = document.getElementById('lbs-context-menu');
    if (existingMenu) {
      existingMenu.remove();
    }

    const menu = document.createElement('div');
    menu.id = 'lbs-context-menu';
    menu.innerHTML = `
      <div class="lbs-context-item" data-action="reset-position">重置位置</div>
      <div class="lbs-context-item" data-action="toggle-auto-hide">切换自动隐藏</div>
      <div class="lbs-context-item" data-action="export-settings">导出设置</div>
      <div class="lbs-context-item" data-action="about">关于插件</div>
    `;

    // 添加菜单样式
    menu.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      min-width: 120px;
    `;

    // 添加菜单项样式
    const style = document.createElement('style');
    style.textContent = `
      .lbs-context-item {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 13px;
        border-bottom: 1px solid #f0f0f0;
      }
      .lbs-context-item:last-child {
        border-bottom: none;
      }
      .lbs-context-item:hover {
        background: #f5f5f5;
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(menu);

    // 绑定菜单事件
    menu.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      this.handleContextAction(action);
      menu.remove();
    });

    // 点击其他地方关闭菜单
    setTimeout(() => {
      document.addEventListener('click', () => {
        menu.remove();
      }, { once: true });
    }, 100);
  }

  handleContextAction(action) {
    switch (action) {
      case 'reset-position':
        this.floatingUI.style.left = 'auto';
        this.floatingUI.style.top = '20px';
        this.floatingUI.style.right = '20px';
        break;
      case 'toggle-auto-hide':
        // 切换自动隐藏功能
        this.showStatus('自动隐藏功能已切换', 'info');
        break;
      case 'export-settings':
        this.exportSettings();
        break;
      case 'about':
        this.showAbout();
        break;
    }
  }

  exportSettings() {
    const settings = {
      searchKeyword: this.searchKeyword,
      searchLocation: this.searchLocation,
      collectedCount: this.collectedData.length,
      version: '1.0.0'
    };

    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'lbs-collector-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  }

  showAbout() {
    this.showStatus('LBS数据采集器 v1.0.0 - 悬浮UI版本', 'info');
  }

  toggleDebugPanel() {
    const debugPanel = document.getElementById('lbs-debug-panel');
    const toggleBtn = document.getElementById('lbs-toggle-debug');

    if (debugPanel.style.display === 'none') {
      debugPanel.style.display = 'block';
      toggleBtn.textContent = '隐藏调试';
      this.updateDebugInfo();
    } else {
      debugPanel.style.display = 'none';
      toggleBtn.textContent = '显示调试';
    }
  }

  updateDebugInfo() {
    const debugContent = document.getElementById('lbs-debug-content');
    if (!debugContent) return;

    const debugInfo = {
      '采集状态': this.isCollecting ? '进行中' : '已停止',
      '搜索关键词': this.searchKeyword || '未设置',
      '搜索地区': this.searchLocation || '未设置',
      '已采集数据': this.collectedData.length,
      '滚动次数': `${this.scrollAttempts}/${this.maxScrollAttempts}`,
      '定时器状态': this.collectionInterval ? '运行中' : '已停止',
      '面板状态': this.isExpanded ? '展开' : '收缩',
      '当前时间': new Date().toLocaleTimeString()
    };

    debugContent.textContent = Object.entries(debugInfo)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 数据采集相关方法
  getPlaceElements() {
    console.log('🔍 [LBS采集器] 开始查找商家元素...');

    // 更全面的选择器，适应不同的Google地图布局
    const selectors = [
      // 新版Google Maps选择器
      '[data-result-index]',
      'div[role="article"]',
      'div[jsaction*="mouseover"]',
      'div[data-cid]',

      // 搜索结果列表项
      '.Nv2PK',
      '.bfdHYd',
      '.lI9IFe',
      '.VkpGBb',

      // 通用容器选择器
      '[aria-label*="results"] > div',
      '[role="main"] div[jsaction]',
      '.section-result',

      // 备用选择器
      'div[data-value]',
      'a[data-cid]',
      '.place-result'
    ];

    for (let selector of selectors) {
      console.log(`🔍 [LBS采集器] 尝试选择器: ${selector}`);
      const elements = document.querySelectorAll(selector);
      console.log(`🔍 [LBS采集器] 选择器 "${selector}" 找到 ${elements.length} 个元素`);

      if (elements.length > 0) {
        // 过滤掉明显不是商家的元素
        const validElements = Array.from(elements).filter(el => {
          // 检查元素是否包含文本内容
          const hasText = el.textContent && el.textContent.trim().length > 0;
          // 检查元素大小（避免隐藏元素）
          const rect = el.getBoundingClientRect();
          const hasSize = rect.width > 0 && rect.height > 0;
          // 检查是否包含可能的商家信息
          const hasBusinessInfo = el.textContent && (
            el.textContent.includes('★') ||
            el.textContent.includes('·') ||
            el.querySelector('span') ||
            el.querySelector('div')
          );

          return hasText && hasSize && hasBusinessInfo;
        });

        console.log(`✅ [LBS采集器] 选择器 "${selector}" 过滤后有效元素: ${validElements.length}`);

        if (validElements.length > 0) {
          return validElements;
        }
      }
    }

    console.warn('⚠️ [LBS采集器] 未找到任何商家元素');
    return [];
  }

  extractPlaceData(element) {
    console.log('📊 [LBS采集器] 开始提取元素数据...');
    console.log('📊 [LBS采集器] 元素HTML预览:', element.outerHTML.substring(0, 200) + '...');

    const data = {
      name: '',
      address: '',
      phone: '',
      rating: '',
      reviewCount: '',
      type: '',
      website: '',
      hours: '',
      timestamp: new Date().toISOString()
    };

    try {
      // 提取商家名称 - 更全面的选择器
      console.log('🏷️ [LBS采集器] 开始提取商家名称...');
      const nameSelectors = [
        // 新版Google Maps选择器
        '.qBF1Pd',
        '.DUwDvf',
        '.fontHeadlineSmall',
        '.fontHeadlineLarge',

        // 通用选择器
        '[data-value="Name"]',
        'h3',
        'h2',
        'h1',

        // 搜索结果选择器
        '.section-result-title',
        '.section-result-title span',
        'a[data-value="title"] span',

        // 链接和按钮选择器
        'a[href*="place"] span',
        'button[data-value] span',

        // 备用选择器
        '.place-name',
        '.business-name',
        '[role="button"] span',
        'div[jsaction] span:first-child'
      ];

      for (let selector of nameSelectors) {
        const nameEl = element.querySelector(selector);
        console.log(`🏷️ [LBS采集器] 尝试名称选择器: ${selector} -> ${nameEl ? '找到' : '未找到'}`);
        if (nameEl && nameEl.textContent.trim()) {
          data.name = nameEl.textContent.trim();
          console.log(`✅ [LBS采集器] 提取到商家名称: "${data.name}"`);
          break;
        }
      }

      // 如果没有找到名称，尝试从元素的直接文本中提取
      if (!data.name) {
        console.log('🔍 [LBS采集器] 尝试从元素文本中提取名称...');
        const textContent = element.textContent.trim();
        const lines = textContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

        if (lines.length > 0) {
          // 通常第一行是商家名称
          const firstLine = lines[0];
          // 过滤掉明显不是名称的内容
          if (firstLine &&
              !firstLine.match(/^\d+\.?\d*$/) && // 不是纯数字
              !firstLine.includes('★') && // 不包含星号
              !firstLine.includes('·') && // 不包含分隔符
              firstLine.length > 1 && firstLine.length < 100) { // 长度合理
            data.name = firstLine;
            console.log(`✅ [LBS采集器] 从文本提取到名称: "${data.name}"`);
          }
        }
      }

      // 提取地址 - 改进的地址识别
      console.log('📍 [LBS采集器] 开始提取地址信息...');
      const addressSelectors = [
        // 新版选择器
        '.W4Efsd:last-child',
        '.W4Efsd[span]',
        '.W4Efsd:nth-child(2)',

        // 通用选择器
        '[data-value="Address"]',
        '.rogA2c',
        '.section-result-location',
        '.section-result-details .fontBodyMedium',

        // 备用选择器
        '.address',
        '.location',
        '[aria-label*="address"]',
        'span[title]'
      ];

      for (let selector of addressSelectors) {
        const addressEl = element.querySelector(selector);
        console.log(`📍 [LBS采集器] 尝试地址选择器: ${selector} -> ${addressEl ? '找到' : '未找到'}`);
        if (addressEl && addressEl.textContent.trim()) {
          const addressText = addressEl.textContent.trim();
          // 过滤掉明显不是地址的内容
          if (!addressText.match(/^\d+\.?\d*$/) &&
              !addressText.includes('★') &&
              !addressText.includes('评分') &&
              addressText.length > 5 &&
              addressText.length < 200) {
            data.address = addressText;
            console.log(`✅ [LBS采集器] 提取到地址: "${data.address}"`);
            break;
          }
        }
      }

      // 如果没有找到地址，尝试从文本中智能提取
      if (!data.address) {
        console.log('🔍 [LBS采集器] 尝试从文本中智能提取地址...');
        const textContent = element.textContent.trim();
        const lines = textContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

        for (let line of lines) {
          // 地址通常包含这些关键词
          if ((line.includes('区') || line.includes('路') || line.includes('街') ||
               line.includes('市') || line.includes('省') || line.includes('县') ||
               line.includes('Road') || line.includes('Street') || line.includes('Ave')) &&
              !line.includes('★') && !line.includes('评分') &&
              line.length > 5 && line.length < 200) {
            data.address = line;
            console.log(`✅ [LBS采集器] 智能提取到地址: "${data.address}"`);
            break;
          }
        }
      }

      // 提取评分 - 更精确的评分匹配
      console.log('⭐ [LBS采集器] 开始提取评分信息...');
      const ratingSelectors = [
        '.MW4etd',
        '[data-value="Rating"]',
        '.fontBodyMedium span',
        '.section-result-rating span',
        'span[aria-label*="stars"]',
        'span[aria-label*="rating"]'
      ];

      for (let selector of ratingSelectors) {
        const ratingEl = element.querySelector(selector);
        console.log(`⭐ [LBS采集器] 尝试评分选择器: ${selector} -> ${ratingEl ? '找到' : '未找到'}`);
        if (ratingEl) {
          const ratingText = ratingEl.textContent || ratingEl.getAttribute('aria-label') || '';
          const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
          if (ratingMatch && parseFloat(ratingMatch[1]) <= 5 && parseFloat(ratingMatch[1]) >= 0) {
            data.rating = ratingMatch[1];
            console.log(`✅ [LBS采集器] 提取到评分: ${data.rating}`);
            break;
          }
        }
      }

      // 从文本中查找评分
      if (!data.rating) {
        console.log('🔍 [LBS采集器] 尝试从文本中查找评分...');
        const textContent = element.textContent;
        const ratingMatch = textContent.match(/(\d+\.?\d*)\s*★/);
        if (ratingMatch && parseFloat(ratingMatch[1]) <= 5 && parseFloat(ratingMatch[1]) >= 0) {
          data.rating = ratingMatch[1];
          console.log(`✅ [LBS采集器] 从文本提取到评分: ${data.rating}`);
        }
      }

      // 提取评论数 - 改进的评论数识别
      console.log('💬 [LBS采集器] 开始提取评论数...');
      const reviewSelectors = [
        '.UY7F9',
        '[data-value="Reviews"]',
        '.section-result-num-reviews',
        'span[aria-label*="reviews"]'
      ];

      for (let selector of reviewSelectors) {
        const reviewEl = element.querySelector(selector);
        console.log(`💬 [LBS采集器] 尝试评论选择器: ${selector} -> ${reviewEl ? '找到' : '未找到'}`);
        if (reviewEl) {
          const reviewText = reviewEl.textContent.trim();
          const reviewMatch = reviewText.match(/\(([^)]+)\)/) || reviewText.match(/(\d+)\s*条?评/);
          if (reviewMatch) {
            data.reviewCount = reviewMatch[1];
            console.log(`✅ [LBS采集器] 提取到评论数: ${data.reviewCount}`);
            break;
          }
        }
      }

      // 从文本中查找评论数
      if (!data.reviewCount) {
        console.log('🔍 [LBS采集器] 尝试从文本中查找评论数...');
        const textContent = element.textContent;
        const reviewMatch = textContent.match(/\((\d+[,\d]*)\)/) || textContent.match(/(\d+[,\d]*)\s*条?评/);
        if (reviewMatch) {
          data.reviewCount = reviewMatch[1];
          console.log(`✅ [LBS采集器] 从文本提取到评论数: ${data.reviewCount}`);
        }
      }

      // 提取商家类型 - 更智能的类型识别
      console.log('🏷️ [LBS采集器] 开始提取商家类型...');
      const typeSelectors = [
        '.W4Efsd:first-child',
        '[data-value="Category"]',
        '.section-result-details .fontBodyMedium:first-child',
        'span[data-value="category"]',
        '.category'
      ];

      for (let selector of typeSelectors) {
        const typeEl = element.querySelector(selector);
        console.log(`🏷️ [LBS采集器] 尝试类型选择器: ${selector} -> ${typeEl ? '找到' : '未找到'}`);
        if (typeEl && typeEl.textContent.trim()) {
          const typeText = typeEl.textContent.trim();
          // 过滤掉地址、评分等非类型信息
          if (!typeText.includes('·') &&
              !typeText.match(/\d+\.?\d*/) &&
              !typeText.includes('★') &&
              !typeText.includes('区') &&
              !typeText.includes('路') &&
              !typeText.includes('街') &&
              typeText.length < 50 &&
              typeText.length > 1) {
            data.type = typeText;
            console.log(`✅ [LBS采集器] 提取到商家类型: "${data.type}"`);
            break;
          }
        }
      }

      // 从文本中智能提取类型
      if (!data.type) {
        console.log('🔍 [LBS采集器] 尝试从文本中智能提取类型...');
        const textContent = element.textContent;
        const lines = textContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

        // 常见的商家类型关键词
        const typeKeywords = ['餐厅', '咖啡', '酒店', '商店', '超市', '银行', '医院', '学校', '公园',
                             '加油站', '药店', '理发', '美容', '健身', '电影院', '书店', '服装',
                             'Restaurant', 'Cafe', 'Hotel', 'Store', 'Bank', 'Hospital'];

        for (let line of lines) {
          if (typeKeywords.some(keyword => line.includes(keyword)) &&
              !line.includes('★') && !line.includes('区') && !line.includes('路') &&
              line.length < 30) {
            data.type = line;
            console.log(`✅ [LBS采集器] 智能提取到类型: "${data.type}"`);
            break;
          }
        }
      }

      // 输出提取结果摘要
      console.log('📊 [LBS采集器] 数据提取完成，结果摘要:', {
        名称: data.name || '❌ 未提取到',
        地址: data.address || '❌ 未提取到',
        评分: data.rating || '❌ 未提取到',
        评论数: data.reviewCount || '❌ 未提取到',
        类型: data.type || '❌ 未提取到'
      });

      // 检查数据完整性
      const completeness = this.calculateDataCompleteness(data);
      console.log(`📊 [LBS采集器] 数据完整度: ${completeness}%`);

      if (!data.name) {
        console.warn('⚠️ [LBS采集器] 警告: 未提取到商家名称，这可能导致数据被忽略');
        console.log('🔍 [LBS采集器] 元素完整HTML:', element.outerHTML);
      }

      // 异步获取详细信息
      setTimeout(() => this.clickForDetails(element, data), 500);

    } catch (error) {
      console.error('❌ [LBS采集器] 数据提取错误:', error);
      console.log('🔍 [LBS采集器] 出错元素HTML:', element.outerHTML);
    }

    return data;
  }

  calculateDataCompleteness(data) {
    const fields = ['name', 'address', 'rating', 'type'];
    const filledFields = fields.filter(field => data[field] && data[field].trim()).length;
    return Math.round((filledFields / fields.length) * 100);
  }

  // 页面结构分析工具
  analyzePageStructure() {
    console.log('🔍 [LBS采集器] 开始分析页面结构...');

    // 分析所有可能的商家元素
    const allDivs = document.querySelectorAll('div');
    const businessLikeElements = Array.from(allDivs).filter(div => {
      const text = div.textContent.trim();
      return text.includes('★') ||
             text.includes('·') ||
             text.match(/\d+\.?\d*\s*(km|米|公里)/) ||
             (text.length > 10 && text.length < 200 && div.children.length > 0);
    });

    console.log(`📊 [LBS采集器] 找到 ${businessLikeElements.length} 个疑似商家元素`);

    if (businessLikeElements.length > 0) {
      // 分析前5个元素的结构
      businessLikeElements.slice(0, 5).forEach((el, index) => {
        console.log(`🔍 [LBS采集器] 分析元素 ${index + 1}:`);
        console.log(`  文本: ${el.textContent.substring(0, 100)}...`);
        console.log(`  类名: ${el.className}`);
        console.log(`  属性:`, Array.from(el.attributes).map(attr => `${attr.name}="${attr.value}"`));
        console.log(`  子元素数: ${el.children.length}`);

        // 尝试提取数据
        const testData = this.extractPlaceData(el);
        console.log(`  提取结果:`, testData);
      });
    }

    return businessLikeElements;
  }

  // 手动测试数据提取
  testDataExtraction() {
    console.log('🧪 [LBS采集器] 开始手动测试数据提取...');

    const elements = this.getPlaceElements();
    console.log(`🔍 [LBS采集器] 当前方法找到 ${elements.length} 个元素`);

    if (elements.length === 0) {
      console.log('🔍 [LBS采集器] 尝试分析页面结构...');
      const businessElements = this.analyzePageStructure();

      if (businessElements.length > 0) {
        console.log('💡 [LBS采集器] 建议更新选择器以包含这些元素');
      }
    } else {
      // 测试前3个元素的数据提取
      elements.slice(0, 3).forEach((el, index) => {
        console.log(`🧪 [LBS采集器] 测试元素 ${index + 1} 数据提取:`);
        const data = this.extractPlaceData(el);
        console.log(`  结果:`, data);
      });
    }
  }

  async clickForDetails(element, data) {
    try {
      // 保存当前滚动位置
      const resultsContainer = document.querySelector('[role="main"]') ||
                              document.querySelector('.m6QErb') ||
                              document.querySelector('#pane');
      const scrollTop = resultsContainer ? resultsContainer.scrollTop : 0;

      // 模拟点击获取更多详细信息
      element.click();
      await this.sleep(1500);

      // 尝试获取电话号码
      const phoneSelectors = [
        '[data-item-id*="phone"]',
        '[data-value="Phone number"]',
        'button[data-value*="phone"]',
        '[aria-label*="phone"]',
        '[aria-label*="电话"]'
      ];

      for (let selector of phoneSelectors) {
        const phoneEl = document.querySelector(selector);
        if (phoneEl && phoneEl.textContent.trim()) {
          const phoneText = phoneEl.textContent.trim();
          if (phoneText.match(/[\d\+\-\(\)\s]/)) {
            data.phone = phoneText;
            break;
          }
        }
      }

      // 尝试获取网站
      const websiteSelectors = [
        '[data-item-id*="authority"]',
        '[data-value="Website"]',
        'a[href*="http"]',
        '[aria-label*="website"]',
        '[aria-label*="网站"]'
      ];

      for (let selector of websiteSelectors) {
        const websiteEl = document.querySelector(selector);
        if (websiteEl) {
          const website = websiteEl.href || websiteEl.textContent.trim();
          if (website.startsWith('http')) {
            data.website = website;
            break;
          }
        }
      }

      // 尝试获取营业时间
      const hoursSelectors = [
        '[data-item-id*="oh"]',
        '[data-value="Hours"]',
        '[aria-label*="hours"]',
        '[aria-label*="营业时间"]'
      ];

      for (let selector of hoursSelectors) {
        const hoursEl = document.querySelector(selector);
        if (hoursEl && hoursEl.textContent.trim()) {
          data.hours = hoursEl.textContent.trim();
          break;
        }
      }

      // 恢复滚动位置
      if (resultsContainer) {
        resultsContainer.scrollTop = scrollTop;
      }

    } catch (error) {
      console.error('获取详细信息失败:', error);
    }
  }

  scrollToLoadMore() {
    if (!this.isCollecting) {
      console.log('🛑 [LBS采集器] 采集已停止，跳过滚动');
      return;
    }

    console.log('📜 [LBS采集器] 滚动加载更多数据...');
    const resultsContainer = document.querySelector('[role="main"]') ||
                           document.querySelector('.m6QErb') ||
                           document.querySelector('#pane');

    if (resultsContainer) {
      const beforeScroll = resultsContainer.scrollTop;
      resultsContainer.scrollTop = resultsContainer.scrollHeight;
      this.scrollAttempts++;

      console.log(`📜 [LBS采集器] 滚动执行 ${this.scrollAttempts}/${this.maxScrollAttempts} - 从 ${beforeScroll} 到 ${resultsContainer.scrollTop}`);

      if (this.scrollAttempts >= this.maxScrollAttempts) {
        console.log('🏁 [LBS采集器] 达到最大滚动次数，采集完成');
        this.stopCollection();
      }
    } else {
      console.warn('⚠️ [LBS采集器] 未找到结果容器，无法滚动');
    }
  }

  collectCurrentPageData() {
    if (!this.isCollecting) {
      console.log('🛑 [LBS采集器] 采集已停止，跳过数据采集');
      return;
    }

    console.log('📊 [LBS采集器] 开始采集当前页面数据...');
    const placeElements = this.getPlaceElements();
    console.log(`🔍 [LBS采集器] 找到 ${placeElements.length} 个商家元素`);

    let newDataCount = 0;
    placeElements.forEach((element, index) => {
      if (!this.isCollecting) {
        console.log('🛑 [LBS采集器] 采集过程中检测到停止信号');
        return;
      }

      try {
        const placeData = this.extractPlaceData(element);
        if (placeData && placeData.name) {
          const placeKey = `${placeData.name}-${placeData.address}`;
          if (!this.collectedPlaces.has(placeKey)) {
            this.collectedPlaces.add(placeKey);
            this.addCollectedData(placeData);
            newDataCount++;
            console.log(`✅ [LBS采集器] 新采集 [${newDataCount}]: ${placeData.name} - ${placeData.address}`);
          } else {
            console.log(`⚠️ [LBS采集器] 跳过重复数据: ${placeData.name}`);
          }
        } else {
          console.log(`❌ [LBS采集器] 元素 ${index + 1} 数据提取失败或无名称`);
        }
      } catch (error) {
        console.error(`❌ [LBS采集器] 元素 ${index + 1} 提取数据失败:`, error);
      }
    });

    console.log(`📊 [LBS采集器] 本轮采集完成 - 新增: ${newDataCount}, 总计: ${this.collectedData.length}`);
  }

}

// 初始化采集器
const collector = new GoogleMapsCollector();
