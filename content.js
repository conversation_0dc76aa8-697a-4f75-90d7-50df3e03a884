// content.js - Google地图页面数据采集脚本（悬浮UI版本）
class GoogleMapsCollector {
  constructor() {
    this.isCollecting = false;
    this.collectedPlaces = new Set();
    this.collectedData = [];
    this.searchKeyword = '';
    this.searchLocation = '';
    this.collectionInterval = null;
    this.scrollAttempts = 0;
    this.maxScrollAttempts = 50;

    // UI相关属性
    this.floatingUI = null;
    this.isExpanded = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };

    this.init();
  }

  init() {
    // 等待页面加载完成后创建UI
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.createFloatingUI());
    } else {
      this.createFloatingUI();
    }

    // 加载存储的数据
    this.loadStoredData();

    console.log('LBS数据采集器已加载（悬浮UI模式）');
  }

  createFloatingUI() {
    // 创建悬浮容器
    this.floatingUI = document.createElement('div');
    this.floatingUI.id = 'lbs-collector-floating-ui';
    this.floatingUI.innerHTML = this.getUIHTML();

    // 添加样式
    this.addUIStyles();

    // 添加到页面
    document.body.appendChild(this.floatingUI);

    // 绑定事件
    this.bindUIEvents();

    // 初始化UI状态
    this.updateUI();
  }

  getUIHTML() {
    return `
      <!-- 悬浮按钮 -->
      <div class="lbs-floating-button" id="lbs-floating-btn">
        <div class="lbs-btn-icon">🗺️</div>
        <div class="lbs-btn-text">LBS采集</div>
      </div>

      <!-- 主面板 -->
      <div class="lbs-main-panel" id="lbs-main-panel">
        <div class="lbs-panel-header">
          <div class="lbs-panel-title">
            <span class="lbs-title-icon">🗺️</span>
            <span class="lbs-title-text">LBS数据采集器</span>
          </div>
          <div class="lbs-panel-controls">
            <button class="lbs-control-btn" id="lbs-minimize-btn" title="最小化">−</button>
            <button class="lbs-control-btn" id="lbs-close-btn" title="关闭">×</button>
          </div>
        </div>

        <div class="lbs-panel-content">
          <div class="lbs-search-section">
            <div class="lbs-input-group">
              <label for="lbs-keyword">搜索关键词:</label>
              <input type="text" id="lbs-keyword" placeholder="例如: 餐厅, 咖啡店, 超市" />
            </div>

            <div class="lbs-input-group">
              <label for="lbs-location">地区 (可选):</label>
              <input type="text" id="lbs-location" placeholder="例如: 北京, 上海" />
            </div>
          </div>

          <div class="lbs-button-group">
            <button id="lbs-start-btn" class="lbs-btn lbs-btn-primary">开始采集</button>
            <button id="lbs-stop-btn" class="lbs-btn lbs-btn-secondary" disabled>停止采集</button>
          </div>

          <div id="lbs-status" class="lbs-status"></div>

          <div id="lbs-data-summary" class="lbs-data-summary">
            <div id="lbs-data-count" class="lbs-data-count">已采集: 0 条数据</div>
            <div class="lbs-progress-bar">
              <div id="lbs-progress-fill" class="lbs-progress-fill"></div>
            </div>
            <div id="lbs-data-preview" class="lbs-data-preview"></div>
          </div>

          <div class="lbs-button-group">
            <button id="lbs-export-btn" class="lbs-btn lbs-btn-success" disabled>导出CSV</button>
            <button id="lbs-clear-btn" class="lbs-btn lbs-btn-secondary">清空数据</button>
          </div>
        </div>
      </div>
    `;
  }

  addUIStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* 悬浮UI基础样式 */
      #lbs-collector-floating-ui {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 999999;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 悬浮按钮样式 */
      .lbs-floating-button {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #1a73e8, #1557b0);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
        transition: all 0.3s ease;
        user-select: none;
      }

      .lbs-floating-button:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(26, 115, 232, 0.4);
      }

      .lbs-btn-icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      .lbs-btn-text {
        font-size: 8px;
        color: white;
        font-weight: 500;
      }

      /* 主面板样式 */
      .lbs-main-panel {
        width: 350px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        border: 1px solid #e0e0e0;
        display: none;
        overflow: hidden;
        animation: slideIn 0.3s ease-out;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(-20px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      /* 面板头部 */
      .lbs-panel-header {
        background: linear-gradient(135deg, #1a73e8, #1557b0);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: move;
      }

      .lbs-panel-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
      }

      .lbs-title-icon {
        font-size: 16px;
      }

      .lbs-panel-controls {
        display: flex;
        gap: 4px;
      }

      .lbs-control-btn {
        width: 24px;
        height: 24px;
        border: none;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        transition: background 0.2s;
      }

      .lbs-control-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      /* 面板内容 */
      .lbs-panel-content {
        padding: 20px;
      }

      .lbs-search-section {
        margin-bottom: 20px;
      }

      .lbs-input-group {
        margin-bottom: 15px;
      }

      .lbs-input-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
      }

      .lbs-input-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        box-sizing: border-box;
        transition: border-color 0.2s, box-shadow 0.2s;
      }

      .lbs-input-group input:focus {
        outline: none;
        border-color: #1a73e8;
        box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
      }

      /* 按钮样式 */
      .lbs-button-group {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .lbs-btn {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        user-select: none;
      }

      .lbs-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .lbs-btn-primary {
        background: #1a73e8;
        color: white;
      }

      .lbs-btn-primary:hover:not(:disabled) {
        background: #1557b0;
        transform: translateY(-1px);
      }

      .lbs-btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #ddd;
      }

      .lbs-btn-secondary:hover:not(:disabled) {
        background: #e8f0fe;
        border-color: #1a73e8;
      }

      .lbs-btn-success {
        background: #34a853;
        color: white;
      }

      .lbs-btn-success:hover:not(:disabled) {
        background: #2d8f47;
        transform: translateY(-1px);
      }

      /* 状态显示 */
      .lbs-status {
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        font-size: 14px;
        display: none;
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      .lbs-status.info {
        background: #e8f0fe;
        color: #1a73e8;
        border: 1px solid #dadce0;
      }

      .lbs-status.success {
        background: #e6f4ea;
        color: #137333;
        border: 1px solid #34a853;
      }

      .lbs-status.error {
        background: #fce8e6;
        color: #d93025;
        border: 1px solid #ea4335;
      }

      /* 数据摘要 */
      .lbs-data-summary {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 15px;
        display: none;
      }

      .lbs-data-count {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 10px;
      }

      .lbs-progress-bar {
        width: 100%;
        height: 6px;
        background: #e0e0e0;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 10px;
      }

      .lbs-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #1a73e8, #34a853);
        width: 0%;
        transition: width 0.3s ease;
      }

      .lbs-data-preview {
        max-height: 150px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 10px;
        font-size: 12px;
        background: white;
      }

      .lbs-data-item {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .lbs-data-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .lbs-business-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
      }

      .lbs-business-details {
        color: #666;
        font-size: 11px;
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        #lbs-collector-floating-ui {
          right: 10px;
          top: 10px;
        }

        .lbs-main-panel {
          width: calc(100vw - 20px);
          max-width: 350px;
        }
      }

      /* 拖拽状态 */
      .lbs-dragging {
        cursor: move !important;
        user-select: none;
      }

      .lbs-dragging .lbs-main-panel {
        transform: rotate(2deg);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
      }
    `;

    document.head.appendChild(style);
  }

  bindUIEvents() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');
    const minimizeBtn = document.getElementById('lbs-minimize-btn');
    const closeBtn = document.getElementById('lbs-close-btn');
    const startBtn = document.getElementById('lbs-start-btn');
    const stopBtn = document.getElementById('lbs-stop-btn');
    const exportBtn = document.getElementById('lbs-export-btn');
    const clearBtn = document.getElementById('lbs-clear-btn');
    const panelHeader = document.querySelector('.lbs-panel-header');

    // 悬浮按钮点击事件
    floatingBtn.addEventListener('click', () => this.togglePanel());

    // 最小化按钮
    minimizeBtn.addEventListener('click', () => this.minimizePanel());

    // 关闭按钮
    closeBtn.addEventListener('click', () => this.closePanel());

    // 功能按钮
    startBtn.addEventListener('click', () => this.startCollection());
    stopBtn.addEventListener('click', () => this.stopCollection());
    exportBtn.addEventListener('click', () => this.exportCsv());
    clearBtn.addEventListener('click', () => this.clearData());

    // 拖拽功能
    this.setupDragFunctionality(panelHeader);

    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));

    // 点击外部关闭面板
    document.addEventListener('click', (e) => {
      if (this.isExpanded && !this.floatingUI.contains(e.target)) {
        this.minimizePanel();
      }
    });
  }

  setupDragFunctionality(dragHandle) {
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    dragHandle.addEventListener('mousedown', (e) => {
      isDragging = true;
      this.isDragging = true;

      const rect = this.floatingUI.getBoundingClientRect();
      startX = e.clientX;
      startY = e.clientY;
      startLeft = rect.left;
      startTop = rect.top;

      this.floatingUI.classList.add('lbs-dragging');

      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newLeft = startLeft + deltaX;
      let newTop = startTop + deltaY;

      // 边界检查
      const maxLeft = window.innerWidth - this.floatingUI.offsetWidth;
      const maxTop = window.innerHeight - this.floatingUI.offsetHeight;

      newLeft = Math.max(0, Math.min(newLeft, maxLeft));
      newTop = Math.max(0, Math.min(newTop, maxTop));

      this.floatingUI.style.left = newLeft + 'px';
      this.floatingUI.style.top = newTop + 'px';
      this.floatingUI.style.right = 'auto';
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        this.isDragging = false;
        this.floatingUI.classList.remove('lbs-dragging');
      }
    });
  }

  handleKeyboard(e) {
    // Ctrl + Shift + L 切换面板
    if (e.ctrlKey && e.shiftKey && e.key === 'L') {
      e.preventDefault();
      this.togglePanel();
    }

    // ESC 关闭面板
    if (e.key === 'Escape' && this.isExpanded) {
      this.minimizePanel();
    }
  }

  togglePanel() {
    if (this.isExpanded) {
      this.minimizePanel();
    } else {
      this.expandPanel();
    }
  }

  expandPanel() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');

    floatingBtn.style.display = 'none';
    mainPanel.style.display = 'block';
    this.isExpanded = true;

    // 更新UI状态
    this.updateUI();
  }

  minimizePanel() {
    const floatingBtn = document.getElementById('lbs-floating-btn');
    const mainPanel = document.getElementById('lbs-main-panel');

    mainPanel.style.display = 'none';
    floatingBtn.style.display = 'flex';
    this.isExpanded = false;
  }

  closePanel() {
    this.floatingUI.style.display = 'none';
  }

  // 数据管理方法
  async loadStoredData() {
    try {
      const result = await chrome.storage.local.get(['lbsData', 'isCollecting']);
      this.collectedData = result.lbsData || [];
      this.isCollecting = result.isCollecting || false;

      // 如果UI已创建，更新显示
      if (this.floatingUI) {
        this.updateUI();
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  }

  async saveData() {
    try {
      await chrome.storage.local.set({
        lbsData: this.collectedData,
        isCollecting: this.isCollecting
      });
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  addCollectedData(newData) {
    // 更严格的重复数据检查
    const exists = this.collectedData.some(item => {
      const nameMatch = item.name === newData.name;
      const addressMatch = item.address === newData.address;
      const phoneMatch = item.phone && newData.phone && item.phone === newData.phone;

      return nameMatch && (addressMatch || phoneMatch);
    });

    if (!exists && newData.name && newData.name.trim()) {
      // 数据清理和标准化
      const cleanedData = this.cleanData(newData);
      this.collectedData.push(cleanedData);
      this.saveData();
      this.updateUI();
      this.showStatus(`已采集 ${this.collectedData.length} 条数据`, 'info');
    }
  }

  cleanData(data) {
    return {
      name: this.cleanText(data.name),
      address: this.cleanText(data.address),
      phone: this.cleanPhone(data.phone),
      rating: this.cleanRating(data.rating),
      reviewCount: this.cleanReviewCount(data.reviewCount),
      type: this.cleanText(data.type),
      website: this.cleanUrl(data.website),
      hours: this.cleanText(data.hours),
      timestamp: data.timestamp || new Date().toISOString()
    };
  }

  cleanText(text) {
    if (!text) return '';
    return text.trim().replace(/\s+/g, ' ');
  }

  cleanPhone(phone) {
    if (!phone) return '';
    return phone.replace(/[^\d\+\-\(\)\s]/g, '').trim();
  }

  cleanRating(rating) {
    if (!rating) return '';
    const match = rating.match(/(\d+\.?\d*)/);
    return match ? match[1] : '';
  }

  cleanReviewCount(reviewCount) {
    if (!reviewCount) return '';
    const match = reviewCount.match(/(\d+)/);
    return match ? match[1] : '';
  }

  cleanUrl(url) {
    if (!url) return '';
    try {
      new URL(url);
      return url;
    } catch {
      return url.startsWith('http') ? url : '';
    }
  }

  updateUI() {
    if (!this.floatingUI) return;

    const startBtn = document.getElementById('lbs-start-btn');
    const stopBtn = document.getElementById('lbs-stop-btn');
    const exportBtn = document.getElementById('lbs-export-btn');
    const dataSummary = document.getElementById('lbs-data-summary');
    const dataCount = document.getElementById('lbs-data-count');
    const dataPreview = document.getElementById('lbs-data-preview');
    const progressFill = document.getElementById('lbs-progress-fill');
    const floatingBtn = document.getElementById('lbs-floating-btn');

    // 更新按钮状态
    if (startBtn && stopBtn && exportBtn) {
      startBtn.disabled = this.isCollecting;
      stopBtn.disabled = !this.isCollecting;
      exportBtn.disabled = this.collectedData.length === 0;

      // 更新按钮文本
      if (this.isCollecting) {
        startBtn.textContent = '采集中...';
        stopBtn.textContent = '停止采集';
      } else {
        startBtn.textContent = '开始采集';
        stopBtn.textContent = '停止采集';
      }

      // 更新导出按钮文本
      if (this.collectedData.length > 0) {
        exportBtn.textContent = `导出CSV (${this.collectedData.length}条)`;
      } else {
        exportBtn.textContent = '导出CSV';
      }
    }

    // 更新悬浮按钮状态
    if (floatingBtn) {
      const btnText = floatingBtn.querySelector('.lbs-btn-text');
      if (this.isCollecting) {
        btnText.textContent = '采集中';
        floatingBtn.style.background = 'linear-gradient(135deg, #34a853, #2d8f47)';
      } else if (this.collectedData.length > 0) {
        btnText.textContent = `${this.collectedData.length}条数据`;
        floatingBtn.style.background = 'linear-gradient(135deg, #1a73e8, #1557b0)';
      } else {
        btnText.textContent = 'LBS采集';
        floatingBtn.style.background = 'linear-gradient(135deg, #1a73e8, #1557b0)';
      }
    }

    // 更新数据摘要
    if (dataSummary && dataCount && dataPreview && progressFill) {
      if (this.collectedData.length > 0) {
        dataSummary.style.display = 'block';

        // 数据统计
        const stats = this.generateDataStats();
        dataCount.innerHTML = `
          <div>已采集: <strong>${this.collectedData.length}</strong> 条数据</div>
          <div style="font-size: 12px; color: #666; margin-top: 5px;">
            📞 ${stats.withPhone}个有电话 | ⭐ ${stats.withRating}个有评分 | 🌐 ${stats.withWebsite}个有网站
          </div>
        `;

        // 进度条
        const progress = Math.min((this.collectedData.length / 50) * 100, 100);
        progressFill.style.width = progress + '%';

        // 数据预览
        const recentData = this.collectedData.slice(-5).reverse();
        dataPreview.innerHTML = recentData.map((item) => {
          const completeness = this.calculateCompleteness(item);
          const completenessColor = completeness >= 80 ? '#34a853' : completeness >= 60 ? '#fbbc04' : '#ea4335';

          return `
            <div class="lbs-data-item">
              <div class="lbs-business-name">
                ${item.name || '未知商家'}
                <span style="float: right; font-size: 10px; color: ${completenessColor};">
                  ${completeness}%
                </span>
              </div>
              <div class="lbs-business-details">
                📍 ${this.truncateText(item.address || '地址未知', 30)}<br>
                📞 ${item.phone || '电话未知'}<br>
                ⭐ ${item.rating || '暂无评分'} ${item.reviewCount ? `(${item.reviewCount}条评论)` : ''}
                ${item.type ? `<br>🏷️ ${item.type}` : ''}
              </div>
            </div>
          `;
        }).join('');
      } else {
        dataSummary.style.display = 'none';
      }
    }
  }

  generateDataStats() {
    const total = this.collectedData.length;
    const withPhone = this.collectedData.filter(item => item.phone && item.phone.trim()).length;
    const withRating = this.collectedData.filter(item => item.rating && item.rating.trim()).length;
    const withWebsite = this.collectedData.filter(item => item.website && item.website.trim()).length;

    const ratings = this.collectedData
      .filter(item => item.rating && !isNaN(parseFloat(item.rating)))
      .map(item => parseFloat(item.rating));

    const avgRating = ratings.length > 0
      ? (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(2)
      : '无';

    return {
      total,
      withPhone,
      withRating,
      withWebsite,
      avgRating
    };
  }

  calculateCompleteness(item) {
    const fields = ['name', 'address', 'phone', 'rating', 'type'];
    const filledFields = fields.filter(field => item[field] && item[field].trim()).length;
    return Math.round((filledFields / fields.length) * 100);
  }

  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  showStatus(message, type) {
    const statusEl = document.getElementById('lbs-status');
    if (!statusEl) return;

    statusEl.textContent = message;
    statusEl.className = `lbs-status ${type}`;
    statusEl.style.display = 'block';

    // 3秒后自动隐藏
    setTimeout(() => {
      statusEl.style.display = 'none';
    }, 3000);
  }

  async exportCsv() {
    if (this.collectedData.length === 0) {
      this.showStatus('没有数据可导出', 'error');
      return;
    }

    try {
      const csvContent = this.generateCsv();
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `lbs_data_${timestamp}.csv`;

      // 创建下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showStatus(`已导出 ${this.collectedData.length} 条数据`, 'success');
    } catch (error) {
      console.error('导出失败:', error);
      this.showStatus('导出失败，请重试', 'error');
    }
  }

  generateCsv() {
    const headers = [
      '商家名称',
      '地址',
      '电话',
      '评分',
      '评论数',
      '商家类型',
      '网站',
      '营业时间',
      '采集时间',
      '数据完整度'
    ];
    const csvRows = [headers.join(',')];

    // 添加数据统计行
    const stats = this.generateDataStats();
    csvRows.push(''); // 空行
    csvRows.push('数据统计信息');
    csvRows.push(`总数据量,${stats.total}`);
    csvRows.push(`有电话号码,${stats.withPhone}`);
    csvRows.push(`有评分,${stats.withRating}`);
    csvRows.push(`有网站,${stats.withWebsite}`);
    csvRows.push(`平均评分,${stats.avgRating}`);
    csvRows.push(''); // 空行
    csvRows.push('详细数据');
    csvRows.push(headers.join(','));

    this.collectedData.forEach(item => {
      const completeness = this.calculateCompleteness(item);
      const row = [
        this.escapeCsvField(item.name || ''),
        this.escapeCsvField(item.address || ''),
        this.escapeCsvField(item.phone || ''),
        this.escapeCsvField(item.rating || ''),
        this.escapeCsvField(item.reviewCount || ''),
        this.escapeCsvField(item.type || ''),
        this.escapeCsvField(item.website || ''),
        this.escapeCsvField(item.hours || ''),
        this.escapeCsvField(item.timestamp || ''),
        this.escapeCsvField(completeness + '%')
      ];
      csvRows.push(row.join(','));
    });

    return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
  }

  escapeCsvField(field) {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  async clearData() {
    if (confirm('确定要清空所有采集的数据吗？')) {
      this.collectedData = [];
      await this.saveData();
      this.updateUI();
      this.showStatus('数据已清空', 'info');
    }
  }

  // 采集功能方法
  async startCollection() {
    const keywordInput = document.getElementById('lbs-keyword');
    const locationInput = document.getElementById('lbs-location');

    const keyword = keywordInput ? keywordInput.value.trim() : '';
    const location = locationInput ? locationInput.value.trim() : '';

    if (!keyword) {
      this.showStatus('请输入搜索关键词', 'error');
      return;
    }

    this.isCollecting = true;
    this.searchKeyword = keyword;
    this.searchLocation = location;
    this.collectedPlaces.clear();
    this.scrollAttempts = 0;

    await this.saveData();
    this.updateUI();
    this.showStatus('开始采集数据...', 'info');

    try {
      // 首先执行搜索
      await this.performSearch(keyword, location);

      // 等待搜索结果加载
      await this.waitForSearchResults();

      // 开始采集数据
      this.startDataCollection();

    } catch (error) {
      console.error('采集启动失败:', error);
      this.showStatus(`采集失败: ${error.message}`, 'error');
      this.isCollecting = false;
      await this.saveData();
      this.updateUI();
    }
  }

  async performSearch(keyword, location) {
    const searchQuery = location ? `${keyword} ${location}` : keyword;

    // 查找搜索框
    const searchBox = document.querySelector('input[data-value="Search"]') ||
                     document.querySelector('#searchboxinput') ||
                     document.querySelector('input[aria-label*="Search"]');

    if (!searchBox) {
      throw new Error('未找到搜索框，请确保在Google地图页面');
    }

    // 清空并输入搜索内容
    searchBox.focus();
    searchBox.value = '';
    searchBox.dispatchEvent(new Event('input', { bubbles: true }));

    // 模拟输入
    for (let char of searchQuery) {
      searchBox.value += char;
      searchBox.dispatchEvent(new Event('input', { bubbles: true }));
      await this.sleep(50);
    }

    // 提交搜索
    searchBox.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));

    console.log(`搜索: ${searchQuery}`);
  }

  async waitForSearchResults() {
    console.log('等待搜索结果加载...');

    for (let i = 0; i < 30; i++) {
      const results = this.getPlaceElements();
      if (results.length > 0) {
        console.log(`找到 ${results.length} 个初始结果`);
        return;
      }
      await this.sleep(1000);
    }

    throw new Error('搜索结果加载超时');
  }

  startDataCollection() {
    console.log('开始数据采集...');

    this.collectionInterval = setInterval(() => {
      if (!this.isCollecting) {
        return;
      }

      this.collectCurrentPageData();
      this.scrollToLoadMore();

    }, 2000);
  }

  collectCurrentPageData() {
    const placeElements = this.getPlaceElements();

    placeElements.forEach(element => {
      try {
        const placeData = this.extractPlaceData(element);
        if (placeData && placeData.name) {
          const placeKey = `${placeData.name}-${placeData.address}`;
          if (!this.collectedPlaces.has(placeKey)) {
            this.collectedPlaces.add(placeKey);
            this.addCollectedData(placeData);
            console.log('采集到:', placeData.name);
          }
        }
      } catch (error) {
        console.error('提取数据失败:', error);
      }
    });
  }

  stopCollection() {
    this.isCollecting = false;
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }

    this.saveData();
    this.updateUI();
    this.showStatus(`采集完成！共采集到 ${this.collectedData.length} 条数据`, 'success');
    console.log('数据采集已停止');
  }

  // 增强交互功能
  setupAutoHide() {
    let hideTimer;

    const startHideTimer = () => {
      clearTimeout(hideTimer);
      hideTimer = setTimeout(() => {
        if (this.isExpanded && !this.isCollecting) {
          this.minimizePanel();
        }
      }, 30000); // 30秒后自动隐藏
    };

    const cancelHideTimer = () => {
      clearTimeout(hideTimer);
    };

    // 鼠标进入取消隐藏，离开开始计时
    this.floatingUI.addEventListener('mouseenter', cancelHideTimer);
    this.floatingUI.addEventListener('mouseleave', startHideTimer);

    // 初始启动计时器
    if (this.isExpanded) {
      startHideTimer();
    }
  }

  setupResizeObserver() {
    // 监听窗口大小变化，调整位置
    const resizeObserver = new ResizeObserver(() => {
      this.adjustPosition();
    });

    resizeObserver.observe(document.body);
  }

  adjustPosition() {
    if (!this.floatingUI) return;

    const rect = this.floatingUI.getBoundingClientRect();
    const maxLeft = window.innerWidth - rect.width;
    const maxTop = window.innerHeight - rect.height;

    let needsAdjustment = false;
    let newLeft = rect.left;
    let newTop = rect.top;

    if (rect.left > maxLeft) {
      newLeft = maxLeft;
      needsAdjustment = true;
    }

    if (rect.top > maxTop) {
      newTop = maxTop;
      needsAdjustment = true;
    }

    if (needsAdjustment) {
      this.floatingUI.style.left = Math.max(0, newLeft) + 'px';
      this.floatingUI.style.top = Math.max(0, newTop) + 'px';
      this.floatingUI.style.right = 'auto';
    }
  }

  // 添加快捷操作
  addQuickActions() {
    // 双击悬浮按钮快速开始采集
    const floatingBtn = document.getElementById('lbs-floating-btn');
    let clickCount = 0;

    floatingBtn.addEventListener('click', () => {
      clickCount++;
      setTimeout(() => {
        if (clickCount === 2) {
          // 双击事件
          this.quickStart();
        }
        clickCount = 0;
      }, 300);
    });
  }

  quickStart() {
    // 如果有上次的搜索关键词，直接开始采集
    if (this.searchKeyword) {
      this.expandPanel();
      setTimeout(() => {
        this.startCollection();
      }, 500);
    } else {
      this.expandPanel();
      // 聚焦到关键词输入框
      setTimeout(() => {
        const keywordInput = document.getElementById('lbs-keyword');
        if (keywordInput) {
          keywordInput.focus();
        }
      }, 300);
    }
  }

  // 添加右键菜单
  addContextMenu() {
    this.floatingUI.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showContextMenu(e.clientX, e.clientY);
    });
  }

  showContextMenu(x, y) {
    // 移除已存在的菜单
    const existingMenu = document.getElementById('lbs-context-menu');
    if (existingMenu) {
      existingMenu.remove();
    }

    const menu = document.createElement('div');
    menu.id = 'lbs-context-menu';
    menu.innerHTML = `
      <div class="lbs-context-item" data-action="reset-position">重置位置</div>
      <div class="lbs-context-item" data-action="toggle-auto-hide">切换自动隐藏</div>
      <div class="lbs-context-item" data-action="export-settings">导出设置</div>
      <div class="lbs-context-item" data-action="about">关于插件</div>
    `;

    // 添加菜单样式
    menu.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000000;
      min-width: 120px;
    `;

    // 添加菜单项样式
    const style = document.createElement('style');
    style.textContent = `
      .lbs-context-item {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 13px;
        border-bottom: 1px solid #f0f0f0;
      }
      .lbs-context-item:last-child {
        border-bottom: none;
      }
      .lbs-context-item:hover {
        background: #f5f5f5;
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(menu);

    // 绑定菜单事件
    menu.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      this.handleContextAction(action);
      menu.remove();
    });

    // 点击其他地方关闭菜单
    setTimeout(() => {
      document.addEventListener('click', () => {
        menu.remove();
      }, { once: true });
    }, 100);
  }

  handleContextAction(action) {
    switch (action) {
      case 'reset-position':
        this.floatingUI.style.left = 'auto';
        this.floatingUI.style.top = '20px';
        this.floatingUI.style.right = '20px';
        break;
      case 'toggle-auto-hide':
        // 切换自动隐藏功能
        this.showStatus('自动隐藏功能已切换', 'info');
        break;
      case 'export-settings':
        this.exportSettings();
        break;
      case 'about':
        this.showAbout();
        break;
    }
  }

  exportSettings() {
    const settings = {
      searchKeyword: this.searchKeyword,
      searchLocation: this.searchLocation,
      collectedCount: this.collectedData.length,
      version: '1.0.0'
    };

    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'lbs-collector-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  }

  showAbout() {
    this.showStatus('LBS数据采集器 v1.0.0 - 悬浮UI版本', 'info');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 数据采集相关方法
  getPlaceElements() {
    // 多种可能的选择器，适应不同的Google地图布局
    const selectors = [
      '[data-result-index]',
      '[role="article"]',
      '.Nv2PK',
      '[jsaction*="mouseover"]',
      '.bfdHYd',
      '[data-cid]'
    ];

    for (let selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        return Array.from(elements);
      }
    }

    return [];
  }

  extractPlaceData(element) {
    const data = {
      name: '',
      address: '',
      phone: '',
      rating: '',
      reviewCount: '',
      type: '',
      website: '',
      hours: '',
      timestamp: new Date().toISOString()
    };

    try {
      // 提取商家名称 - 更全面的选择器
      const nameSelectors = [
        '.qBF1Pd',
        '.DUwDvf',
        '.fontHeadlineSmall',
        '[data-value="Name"]',
        'h3',
        '.section-result-title',
        '.section-result-title span',
        'a[data-value="title"] span'
      ];

      for (let selector of nameSelectors) {
        const nameEl = element.querySelector(selector);
        if (nameEl && nameEl.textContent.trim()) {
          data.name = nameEl.textContent.trim();
          break;
        }
      }

      // 提取地址 - 改进的地址识别
      const addressSelectors = [
        '.W4Efsd:last-child',
        '.W4Efsd[span]',
        '[data-value="Address"]',
        '.rogA2c',
        '.section-result-location',
        '.section-result-details .fontBodyMedium'
      ];

      for (let selector of addressSelectors) {
        const addressEl = element.querySelector(selector);
        if (addressEl && addressEl.textContent.trim()) {
          const addressText = addressEl.textContent.trim();
          // 过滤掉明显不是地址的内容
          if (!addressText.match(/^\d+\.?\d*$/) && !addressText.includes('★') && addressText.length > 5) {
            data.address = addressText;
            break;
          }
        }
      }

      // 提取评分 - 更精确的评分匹配
      const ratingSelectors = [
        '.MW4etd',
        '[data-value="Rating"]',
        '.fontBodyMedium span',
        '.section-result-rating span'
      ];

      for (let selector of ratingSelectors) {
        const ratingEl = element.querySelector(selector);
        if (ratingEl) {
          const ratingMatch = ratingEl.textContent.match(/(\d+\.?\d*)/);
          if (ratingMatch && parseFloat(ratingMatch[1]) <= 5) {
            data.rating = ratingMatch[1];
            break;
          }
        }
      }

      // 提取评论数 - 改进的评论数识别
      const reviewSelectors = [
        '.UY7F9',
        '[data-value="Reviews"]',
        '.section-result-num-reviews'
      ];

      for (let selector of reviewSelectors) {
        const reviewEl = element.querySelector(selector);
        if (reviewEl) {
          const reviewText = reviewEl.textContent.trim();
          const reviewMatch = reviewText.match(/\(([^)]+)\)/);
          if (reviewMatch) {
            data.reviewCount = reviewMatch[1];
            break;
          }
        }
      }

      // 提取商家类型 - 更智能的类型识别
      const typeSelectors = [
        '.W4Efsd:first-child',
        '[data-value="Category"]',
        '.section-result-details .fontBodyMedium:first-child'
      ];

      for (let selector of typeSelectors) {
        const typeEl = element.querySelector(selector);
        if (typeEl && typeEl.textContent.trim()) {
          const typeText = typeEl.textContent.trim();
          // 过滤掉地址、评分等非类型信息
          if (!typeText.includes('·') && !typeText.match(/\d+\.?\d*/) &&
              !typeText.includes('★') && typeText.length < 50) {
            data.type = typeText;
            break;
          }
        }
      }

      // 异步获取详细信息
      setTimeout(() => this.clickForDetails(element, data), 500);

    } catch (error) {
      console.error('数据提取错误:', error);
    }

    return data;
  }

  async clickForDetails(element, data) {
    try {
      // 保存当前滚动位置
      const resultsContainer = document.querySelector('[role="main"]') ||
                              document.querySelector('.m6QErb') ||
                              document.querySelector('#pane');
      const scrollTop = resultsContainer ? resultsContainer.scrollTop : 0;

      // 模拟点击获取更多详细信息
      element.click();
      await this.sleep(1500);

      // 尝试获取电话号码
      const phoneSelectors = [
        '[data-item-id*="phone"]',
        '[data-value="Phone number"]',
        'button[data-value*="phone"]',
        '[aria-label*="phone"]',
        '[aria-label*="电话"]'
      ];

      for (let selector of phoneSelectors) {
        const phoneEl = document.querySelector(selector);
        if (phoneEl && phoneEl.textContent.trim()) {
          const phoneText = phoneEl.textContent.trim();
          if (phoneText.match(/[\d\+\-\(\)\s]/)) {
            data.phone = phoneText;
            break;
          }
        }
      }

      // 尝试获取网站
      const websiteSelectors = [
        '[data-item-id*="authority"]',
        '[data-value="Website"]',
        'a[href*="http"]',
        '[aria-label*="website"]',
        '[aria-label*="网站"]'
      ];

      for (let selector of websiteSelectors) {
        const websiteEl = document.querySelector(selector);
        if (websiteEl) {
          const website = websiteEl.href || websiteEl.textContent.trim();
          if (website.startsWith('http')) {
            data.website = website;
            break;
          }
        }
      }

      // 尝试获取营业时间
      const hoursSelectors = [
        '[data-item-id*="oh"]',
        '[data-value="Hours"]',
        '[aria-label*="hours"]',
        '[aria-label*="营业时间"]'
      ];

      for (let selector of hoursSelectors) {
        const hoursEl = document.querySelector(selector);
        if (hoursEl && hoursEl.textContent.trim()) {
          data.hours = hoursEl.textContent.trim();
          break;
        }
      }

      // 恢复滚动位置
      if (resultsContainer) {
        resultsContainer.scrollTop = scrollTop;
      }

    } catch (error) {
      console.error('获取详细信息失败:', error);
    }
  }

  scrollToLoadMore() {
    const resultsContainer = document.querySelector('[role="main"]') ||
                           document.querySelector('.m6QErb') ||
                           document.querySelector('#pane');

    if (resultsContainer) {
      resultsContainer.scrollTop = resultsContainer.scrollHeight;
      this.scrollAttempts++;

      if (this.scrollAttempts >= this.maxScrollAttempts) {
        console.log('达到最大滚动次数，采集完成');
        this.stopCollection();
      }
    }
  }

  collectCurrentPageData() {
    const placeElements = this.getPlaceElements();

    placeElements.forEach(element => {
      try {
        const placeData = this.extractPlaceData(element);
        if (placeData && placeData.name) {
          const placeKey = `${placeData.name}-${placeData.address}`;
          if (!this.collectedPlaces.has(placeKey)) {
            this.collectedPlaces.add(placeKey);
            this.addCollectedData(placeData);
            console.log('采集到:', placeData.name);
          }
        }
      } catch (error) {
        console.error('提取数据失败:', error);
      }
    });
  }

}

// 初始化采集器
const collector = new GoogleMapsCollector();
