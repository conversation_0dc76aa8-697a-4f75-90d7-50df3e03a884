// background.js - 后台服务脚本
class LBSCollectorBackground {
  constructor() {
    this.init();
  }

  init() {
    // 监听插件安装
    chrome.runtime.onInstalled.addListener(() => {
      console.log('LBS数据采集器已安装');
      this.initializeStorage();
    });

    // 监听消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });
  }

  async initializeStorage() {
    try {
      const result = await chrome.storage.local.get(['lbsData', 'isCollecting']);
      if (!result.lbsData) {
        await chrome.storage.local.set({
          lbsData: [],
          isCollecting: false
        });
      }
    } catch (error) {
      console.error('初始化存储失败:', error);
    }
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'getStoredData':
        this.getStoredData(sendResponse);
        break;
      case 'saveData':
        this.saveData(message.data, sendResponse);
        break;
      case 'clearData':
        this.clearData(sendResponse);
        break;
      case 'exportData':
        this.exportData(sendResponse);
        break;
      default:
        sendResponse({ success: false, error: 'Unknown action' });
    }
  }

  async getStoredData(sendResponse) {
    try {
      const result = await chrome.storage.local.get(['lbsData', 'isCollecting']);
      sendResponse({
        success: true,
        data: result.lbsData || [],
        isCollecting: result.isCollecting || false
      });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async saveData(data, sendResponse) {
    try {
      await chrome.storage.local.set({ lbsData: data });
      sendResponse({ success: true });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async clearData(sendResponse) {
    try {
      await chrome.storage.local.set({
        lbsData: [],
        isCollecting: false
      });
      sendResponse({ success: true });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  async exportData(sendResponse) {
    try {
      const result = await chrome.storage.local.get(['lbsData']);
      const data = result.lbsData || [];
      
      if (data.length === 0) {
        sendResponse({ success: false, error: '没有数据可导出' });
        return;
      }

      // 生成CSV内容
      const csvContent = this.generateCSV(data);
      
      // 创建下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `lbs_data_${timestamp}.csv`;
      
      await chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: true
      });
      
      sendResponse({ success: true, count: data.length });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
  }

  generateCSV(data) {
    const headers = [
      '商家名称',
      '地址', 
      '电话',
      '评分',
      '评论数',
      '商家类型',
      '网站',
      '营业时间',
      '采集时间'
    ];
    
    const csvRows = [headers.join(',')];
    
    data.forEach(item => {
      const row = [
        this.escapeCsvField(item.name || ''),
        this.escapeCsvField(item.address || ''),
        this.escapeCsvField(item.phone || ''),
        this.escapeCsvField(item.rating || ''),
        this.escapeCsvField(item.reviewCount || ''),
        this.escapeCsvField(item.type || ''),
        this.escapeCsvField(item.website || ''),
        this.escapeCsvField(item.hours || ''),
        this.escapeCsvField(item.timestamp || new Date().toISOString())
      ];
      csvRows.push(row.join(','));
    });
    
    // 添加BOM以支持中文
    return '\uFEFF' + csvRows.join('\n');
  }

  escapeCsvField(field) {
    const str = String(field);
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    // 当标签页完成加载且是Google地图页面时，注入content script
    if (changeInfo.status === 'complete' && tab.url) {
      if (tab.url.includes('google.com/maps') || tab.url.includes('maps.google.com')) {
        this.injectContentScript(tabId);
      }
    }
  }

  async injectContentScript(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
    } catch (error) {
      console.error('注入content script失败:', error);
    }
  }
}

// 初始化后台服务
new LBSCollectorBackground();
