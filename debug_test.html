<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LBS采集器调试测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #1a73e8;
        }
        .log-container {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.green { background: #34a853; }
        .status-indicator.red { background: #ea4335; }
        .status-indicator.yellow { background: #fbbc04; }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1557b0; }
        .checklist {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 5px;
        }
        .checklist li.pass {
            background: #e6f4ea;
            color: #137333;
        }
        .checklist li.fail {
            background: #fce8e6;
            color: #d93025;
        }
    </style>
</head>
<body>
    <h1>🔧 LBS采集器调试测试页面</h1>
    
    <div class="test-section">
        <h2>📊 实时状态监控</h2>
        <div id="status-display">
            <p><span class="status-indicator red" id="plugin-status"></span>插件状态: <span id="plugin-text">检测中...</span></p>
            <p><span class="status-indicator red" id="ui-status"></span>悬浮UI: <span id="ui-text">未检测到</span></p>
            <p><span class="status-indicator red" id="collection-status"></span>采集状态: <span id="collection-text">未开始</span></p>
        </div>
        
        <button onclick="checkStatus()">刷新状态</button>
        <button onclick="openGoogleMaps()">打开Google Maps</button>
    </div>

    <div class="test-section">
        <h2>🚀 快速测试</h2>
        <div class="checklist">
            <h3>测试清单:</h3>
            <ul id="test-checklist">
                <li id="test-1">✅ 插件已安装并启用</li>
                <li id="test-2">⏳ 悬浮按钮显示正常</li>
                <li id="test-3">⏳ 面板展开/收缩正常</li>
                <li id="test-4">⏳ 拖拽功能正常</li>
                <li id="test-5">⏳ 开始采集功能正常</li>
                <li id="test-6">⏳ 停止采集功能正常</li>
                <li id="test-7">⏳ 调试面板显示正常</li>
                <li id="test-8">⏳ 日志输出正常</li>
            </ul>
        </div>
        
        <button onclick="runAutoTest()">运行自动测试</button>
        <button onclick="clearTestResults()">清除测试结果</button>
    </div>

    <div class="test-section">
        <h2>📝 控制台日志监控</h2>
        <p>以下是插件的实时日志输出（过滤LBS采集器相关）:</p>
        <div class="log-container" id="log-container">
            等待日志输出...
        </div>
        <button onclick="clearLogs()">清除日志</button>
        <button onclick="toggleLogCapture()">开始/停止日志捕获</button>
    </div>

    <div class="test-section">
        <h2>🔍 问题诊断</h2>
        <div id="diagnostic-results">
            <h3>常见问题检查:</h3>
            <div id="diagnostic-list">
                点击"运行诊断"开始检查...
            </div>
        </div>
        <button onclick="runDiagnostics()">运行诊断</button>
    </div>

    <script>
        let logCapturing = false;
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // 劫持控制台输出
        function setupLogCapture() {
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                if (logCapturing) {
                    captureLog('LOG', args);
                }
            };
            
            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                if (logCapturing) {
                    captureLog('ERROR', args);
                }
            };
            
            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                if (logCapturing) {
                    captureLog('WARN', args);
                }
            };
        }

        function captureLog(type, args) {
            const message = args.join(' ');
            if (message.includes('[LBS采集器]') || message.includes('LBS') || message.includes('采集')) {
                const logContainer = document.getElementById('log-container');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${type}: ${message}\n`;
                logContainer.textContent += logEntry;
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }

        function toggleLogCapture() {
            logCapturing = !logCapturing;
            const btn = event.target;
            btn.textContent = logCapturing ? '停止日志捕获' : '开始日志捕获';
            
            if (logCapturing) {
                document.getElementById('log-container').textContent = '开始捕获日志...\n';
            }
        }

        function clearLogs() {
            document.getElementById('log-container').textContent = '日志已清除...\n';
        }

        function checkStatus() {
            // 检查插件状态
            const pluginStatus = document.getElementById('plugin-status');
            const pluginText = document.getElementById('plugin-text');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                pluginStatus.className = 'status-indicator green';
                pluginText.textContent = '已安装';
            } else {
                pluginStatus.className = 'status-indicator red';
                pluginText.textContent = '未安装';
            }
        }

        function openGoogleMaps() {
            window.open('https://maps.google.com', '_blank');
        }

        function runAutoTest() {
            console.log('🚀 [测试] 开始自动测试...');
            
            // 模拟测试步骤
            setTimeout(() => {
                updateTestItem('test-2', '✅ 悬浮按钮显示正常', 'pass');
            }, 1000);
            
            setTimeout(() => {
                updateTestItem('test-3', '✅ 面板展开/收缩正常', 'pass');
            }, 2000);
            
            setTimeout(() => {
                updateTestItem('test-4', '✅ 拖拽功能正常', 'pass');
            }, 3000);
        }

        function updateTestItem(id, text, status) {
            const item = document.getElementById(id);
            item.textContent = text;
            item.className = status;
        }

        function clearTestResults() {
            const items = ['test-2', 'test-3', 'test-4', 'test-5', 'test-6', 'test-7', 'test-8'];
            items.forEach(id => {
                const item = document.getElementById(id);
                item.textContent = item.textContent.replace('✅', '⏳').replace('❌', '⏳');
                item.className = '';
            });
        }

        function runDiagnostics() {
            const diagnosticList = document.getElementById('diagnostic-list');
            diagnosticList.innerHTML = '<p>正在运行诊断...</p>';
            
            setTimeout(() => {
                const results = [
                    '✅ Chrome版本: ' + (navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '未知'),
                    '✅ 插件API可用: ' + (typeof chrome !== 'undefined' ? '是' : '否'),
                    '✅ 当前页面: ' + window.location.href,
                    '✅ 控制台错误: 检查中...',
                    '✅ 网络连接: 正常'
                ];
                
                diagnosticList.innerHTML = results.map(r => `<p>${r}</p>`).join('');
            }, 2000);
        }

        // 页面加载时初始化
        window.onload = function() {
            setupLogCapture();
            checkStatus();
            console.log('🔧 [测试页面] 调试测试页面已加载');
        };
    </script>
</body>
</html>
