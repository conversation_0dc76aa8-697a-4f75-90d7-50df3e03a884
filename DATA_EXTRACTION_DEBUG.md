# 数据提取调试指南

## 🚨 问题症状

如果你遇到以下情况：
- ❌ 控制台显示"数据提取失败或者无名称"
- ❌ 采集到的数据为空或不完整
- ❌ 商家名称显示为"未知商家"

## 🔧 调试步骤

### 1. 启用详细日志
1. 打开Google Maps页面
2. 按F12打开开发者工具
3. 切换到Console标签
4. 开始采集，观察日志输出

### 2. 使用内置调试工具
1. 展开LBS采集器面板
2. 点击"显示调试"按钮
3. 使用以下调试按钮：
   - **测试提取**: 测试当前页面的数据提取
   - **分析页面**: 分析页面结构，寻找商家元素

### 3. 手动分析页面结构
在控制台中运行以下代码来分析页面：

```javascript
// 查找所有可能的商家元素
const possibleElements = document.querySelectorAll('div');
const businessElements = Array.from(possibleElements).filter(div => {
    const text = div.textContent.trim();
    return text.includes('★') && text.length > 10 && text.length < 200;
});

console.log('找到商家元素:', businessElements.length);
businessElements.slice(0, 3).forEach((el, i) => {
    console.log(`元素 ${i+1}:`, el.textContent.substring(0, 100));
    console.log(`类名:`, el.className);
});
```

## 📊 日志解读

### 正常日志示例
```
🚀 [LBS采集器] 开始启动采集流程...
🔍 [LBS采集器] 开始查找商家元素...
✅ [LBS采集器] 选择器 "[data-result-index]" 找到 8 个元素
📊 [LBS采集器] 开始提取元素数据...
🏷️ [LBS采集器] 开始提取商家名称...
✅ [LBS采集器] 提取到商家名称: "星巴克咖啡"
📍 [LBS采集器] 开始提取地址信息...
✅ [LBS采集器] 提取到地址: "北京市朝阳区建国门外大街1号"
```

### 问题日志示例
```
🔍 [LBS采集器] 选择器 "[data-result-index]" 找到 0 个元素
⚠️ [LBS采集器] 未找到任何商家元素
❌ [LBS采集器] 元素 1 数据提取失败或无名称
⚠️ [LBS采集器] 警告: 未提取到商家名称
```

## 🛠️ 常见问题及解决方案

### 问题1: 找不到商家元素
**症状**: 日志显示"未找到任何商家元素"

**解决方案**:
1. 确认在Google Maps搜索结果页面
2. 等待页面完全加载
3. 尝试手动滚动一下页面
4. 检查是否有搜索结果显示

### 问题2: 找到元素但提取不到名称
**症状**: 找到元素但名称为空

**解决方案**:
1. 使用"分析页面"功能查看元素结构
2. 检查元素的HTML结构是否发生变化
3. 在控制台中手动测试选择器

### 问题3: Google Maps界面更新
**症状**: 之前能用，现在不能用了

**解决方案**:
1. Google Maps经常更新界面结构
2. 使用页面分析工具找到新的选择器
3. 更新插件代码中的选择器

## 🔍 高级调试技巧

### 1. 检查页面元素
```javascript
// 在控制台中运行，查看当前页面的商家元素
function findBusinessElements() {
    const selectors = [
        '[data-result-index]',
        'div[role="article"]',
        'div[jsaction*="mouseover"]'
    ];
    
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        console.log(`${selector}: ${elements.length} 个元素`);
        if (elements.length > 0) {
            console.log('示例:', elements[0].textContent.substring(0, 100));
        }
    });
}

findBusinessElements();
```

### 2. 测试名称提取
```javascript
// 测试名称提取逻辑
function testNameExtraction(element) {
    const nameSelectors = [
        '.qBF1Pd',
        '.DUwDvf',
        '.fontHeadlineSmall',
        'h3'
    ];
    
    nameSelectors.forEach(selector => {
        const nameEl = element.querySelector(selector);
        console.log(`${selector}:`, nameEl ? nameEl.textContent : '未找到');
    });
}

// 对第一个商家元素测试
const firstElement = document.querySelector('[data-result-index]');
if (firstElement) {
    testNameExtraction(firstElement);
}
```

### 3. 分析文本模式
```javascript
// 分析页面中的文本模式
function analyzeTextPatterns() {
    const allText = Array.from(document.querySelectorAll('div'))
        .map(div => div.textContent.trim())
        .filter(text => text.includes('★') && text.length < 200);
    
    console.log('包含星号的文本示例:');
    allText.slice(0, 5).forEach((text, i) => {
        console.log(`${i+1}: ${text}`);
    });
}

analyzeTextPatterns();
```

## 📝 报告问题

如果调试后仍然无法解决，请提供以下信息：

1. **浏览器信息**: Chrome版本
2. **页面URL**: 当前Google Maps页面地址
3. **搜索关键词**: 使用的搜索词
4. **控制台日志**: 完整的错误日志
5. **页面截图**: 显示搜索结果的截图
6. **元素HTML**: 使用"分析页面"功能的输出

## 🔄 临时解决方案

如果数据提取暂时有问题，可以尝试：

1. **更换搜索关键词**: 尝试更具体的关键词
2. **更换地区**: 尝试不同的城市或地区
3. **刷新页面**: 重新加载Google Maps
4. **清除缓存**: 清除浏览器缓存后重试
5. **使用无痕模式**: 在无痕窗口中测试

## 🚀 性能优化

为了提高数据提取成功率：

1. **等待加载**: 给页面足够的加载时间
2. **分批采集**: 不要一次采集太多数据
3. **网络稳定**: 确保网络连接稳定
4. **避免高峰**: 避免在网络高峰期使用

---

**提示**: 数据提取的成功率很大程度上取决于Google Maps的页面结构。如果遇到问题，首先使用调试工具分析页面结构，然后针对性地解决。
