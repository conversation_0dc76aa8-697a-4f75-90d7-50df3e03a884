<!DOCTYPE html>
<html>
<head>
    <title>创建简单图标</title>
</head>
<body>
    <h2>点击按钮创建插件图标</h2>
    <button onclick="createAllIcons()">创建所有图标</button>
    
    <canvas id="canvas" style="display: none;"></canvas>
    
    <div id="status"></div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制背景
            ctx.fillStyle = '#1a73e8';
            ctx.fillRect(0, 0, size, size);
            
            // 绘制简单的地图图标
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${Math.floor(size * 0.6)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('📍', size/2, size/2);
            
            // 转换为blob并下载
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }
        
        function createAllIcons() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '正在创建图标...';
            
            setTimeout(() => createIcon(16), 100);
            setTimeout(() => createIcon(48), 200);
            setTimeout(() => createIcon(128), 300);
            
            setTimeout(() => {
                statusDiv.innerHTML = '图标创建完成！请将下载的图标文件放入 icons/ 文件夹中。';
            }, 1000);
        }
    </script>
</body>
</html>
