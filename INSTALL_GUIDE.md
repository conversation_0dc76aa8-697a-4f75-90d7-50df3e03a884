# Chrome插件安装和测试指南

## 安装步骤

### 1. 准备插件文件
确保以下文件都在项目目录中：
- `manifest.json` - 插件配置文件
- `popup.html` - 弹窗界面
- `popup.js` - 弹窗逻辑
- `content.js` - 内容脚本
- `background.js` - 后台脚本
- `icons/` - 图标文件夹（可选）

### 2. 在Chrome中安装插件

1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择本项目的文件夹
6. 插件安装完成，会在工具栏显示图标

### 3. 权限确认
安装时Chrome会要求确认以下权限：
- 访问当前标签页
- 存储数据
- 下载文件
- 访问Google Maps网站

## 测试步骤

### 基础功能测试

#### 1. 界面测试
- [ ] 点击插件图标，弹窗正常显示
- [ ] 输入框可以正常输入文字
- [ ] 按钮状态正确（开始采集可点击，停止采集禁用）
- [ ] 界面样式正常，无错位

#### 2. 搜索功能测试
- [ ] 打开 [Google Maps](https://maps.google.com)
- [ ] 在插件中输入关键词（如：咖啡店）
- [ ] 点击"开始采集"
- [ ] 观察Google Maps是否自动搜索
- [ ] 检查控制台是否有错误信息

#### 3. 数据采集测试
- [ ] 搜索结果加载后，观察数据采集情况
- [ ] 检查弹窗中的数据计数是否增加
- [ ] 查看数据预览是否显示采集的商家信息
- [ ] 测试停止采集功能

#### 4. 数据导出测试
- [ ] 采集一些数据后，点击"导出CSV"
- [ ] 检查是否弹出下载对话框
- [ ] 下载的CSV文件是否包含正确的数据
- [ ] 用Excel或其他软件打开CSV，检查中文显示

### 高级功能测试

#### 1. 地区限制测试
- [ ] 输入关键词和地区（如：餐厅 北京）
- [ ] 验证搜索结果是否限制在指定地区

#### 2. 数据去重测试
- [ ] 多次采集相同的商家
- [ ] 检查是否有重复数据

#### 3. 数据完整性测试
- [ ] 检查采集的数据字段是否完整
- [ ] 验证电话号码、地址、评分等信息准确性

#### 4. 错误处理测试
- [ ] 在非Google Maps页面使用插件
- [ ] 网络断开时的表现
- [ ] 搜索无结果时的处理

## 常见问题排查

### 插件无法加载
1. 检查manifest.json语法是否正确
2. 确认所有文件路径正确
3. 查看Chrome扩展程序页面的错误信息
4. 如果提示图标文件缺失，确认icons文件夹中有icon16.png、icon48.png、icon128.png文件

### 数据采集不工作
1. 确认在Google Maps页面
2. 检查浏览器控制台错误信息
3. 刷新页面后重试

### CSV导出失败
1. 检查浏览器下载权限
2. 确认有采集到的数据
3. 查看控制台错误信息

### 数据不准确
1. Google Maps页面结构可能发生变化
2. 网络加载速度影响数据提取
3. 某些商家信息本身不完整

## 调试技巧

### 1. 查看控制台日志
- 在Google Maps页面按F12打开开发者工具
- 查看Console标签页的日志信息
- 插件会输出详细的采集过程信息

### 2. 检查存储数据
- 在插件弹窗页面按F12
- 在Console中输入：`chrome.storage.local.get(console.log)`
- 查看存储的数据结构

### 3. 手动测试选择器
- 在Google Maps页面的控制台中
- 使用 `document.querySelector()` 测试CSS选择器
- 验证数据提取逻辑

## 性能优化建议

1. **控制采集频率**：避免过于频繁的请求
2. **合理设置延时**：给页面足够的加载时间
3. **限制采集数量**：避免一次采集过多数据
4. **定期清理数据**：避免存储空间过大

## 法律和道德考虑

⚠️ **重要提醒**
- 仅用于个人学习和研究
- 遵守Google Maps使用条款
- 不要用于商业用途
- 尊重商家隐私权
- 合理控制采集频率
