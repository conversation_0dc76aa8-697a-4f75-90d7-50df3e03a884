<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LBS数据采集器测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #1a73e8;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pending { background: #fff3cd; color: #856404; }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1557b0; }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🗺️ LBS数据采集器测试页面</h1>
    
    <div class="test-section">
        <h2>插件安装检查</h2>
        <div class="test-item">
            <span class="status pending" id="extension-status">检查中...</span>
            插件是否已安装并启用
        </div>
        <button onclick="checkExtension()">检查插件状态</button>
    </div>

    <div class="test-section">
        <h2>功能测试</h2>
        
        <div class="test-item">
            <span class="status pending" id="popup-status">待测试</span>
            弹窗界面显示正常
            <div class="code">测试步骤：点击插件图标，检查弹窗是否正常显示</div>
        </div>

        <div class="test-item">
            <span class="status pending" id="search-status">待测试</span>
            搜索功能正常
            <div class="code">测试步骤：在Google Maps页面使用插件搜索"咖啡店"</div>
        </div>

        <div class="test-item">
            <span class="status pending" id="collection-status">待测试</span>
            数据采集功能正常
            <div class="code">测试步骤：观察数据计数是否增加，预览是否显示商家信息</div>
        </div>

        <div class="test-item">
            <span class="status pending" id="export-status">待测试</span>
            CSV导出功能正常
            <div class="code">测试步骤：采集数据后点击导出，检查CSV文件内容</div>
        </div>
    </div>

    <div class="test-section">
        <h2>快速测试链接</h2>
        <button onclick="openGoogleMaps()">打开Google Maps</button>
        <button onclick="testSearch()">测试搜索（咖啡店）</button>
        <button onclick="testSearch2()">测试搜索（餐厅 北京）</button>
        <div style="margin-top: 10px;">
            <strong>悬浮UI版本测试要点：</strong>
            <ul style="text-align: left; margin-top: 5px;">
                <li>✅ 检查右上角是否出现蓝色悬浮按钮</li>
                <li>✅ 点击悬浮按钮是否能展开面板</li>
                <li>✅ 拖拽标题栏是否能移动面板</li>
                <li>✅ 快捷键 Ctrl+Shift+L 是否有效</li>
                <li>✅ 双击悬浮按钮是否触发快速采集</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>数据验证工具</h2>
        <div>
            <label for="csvFile">上传CSV文件进行验证：</label>
            <input type="file" id="csvFile" accept=".csv" onchange="validateCSV(this)">
        </div>
        <div id="csvResults" style="margin-top: 10px;"></div>
    </div>

    <div class="test-section">
        <h2>调试信息</h2>
        <button onclick="showDebugInfo()">显示调试信息</button>
        <div id="debugInfo" style="margin-top: 10px;"></div>
    </div>

    <script>
        function checkExtension() {
            const statusEl = document.getElementById('extension-status');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                statusEl.textContent = '已安装';
                statusEl.className = 'status pass';
            } else {
                statusEl.textContent = '未安装';
                statusEl.className = 'status fail';
            }
        }

        function openGoogleMaps() {
            window.open('https://maps.google.com', '_blank');
        }

        function testSearch() {
            const url = 'https://maps.google.com/maps?q=咖啡店';
            window.open(url, '_blank');
        }

        function testSearch2() {
            const url = 'https://maps.google.com/maps?q=餐厅+北京';
            window.open(url, '_blank');
        }

        function validateCSV(input) {
            const file = input.files[0];
            const resultsDiv = document.getElementById('csvResults');
            
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n');
                
                let results = '<h4>CSV验证结果：</h4>';
                results += `<p>总行数: ${lines.length}</p>`;
                
                if (lines.length > 0) {
                    const headers = lines[0].split(',');
                    results += `<p>列数: ${headers.length}</p>`;
                    results += `<p>表头: ${headers.join(', ')}</p>`;
                }
                
                // 检查数据完整性
                let validRows = 0;
                for (let i = 1; i < Math.min(lines.length, 10); i++) {
                    const row = lines[i].split(',');
                    if (row.length >= 3 && row[0] && row[1]) {
                        validRows++;
                    }
                }
                
                results += `<p>有效数据行: ${validRows}/${Math.min(lines.length - 1, 9)}</p>`;
                resultsDiv.innerHTML = results;
            };
            
            reader.readAsText(file, 'UTF-8');
        }

        function showDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            
            let info = '<h4>浏览器信息：</h4>';
            info += `<p>User Agent: ${navigator.userAgent}</p>`;
            info += `<p>Chrome版本: ${navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '未知'}</p>`;
            info += `<p>当前时间: ${new Date().toISOString()}</p>`;
            
            if (typeof chrome !== 'undefined') {
                info += '<h4>Chrome扩展API：</h4>';
                info += `<p>chrome.runtime: ${!!chrome.runtime}</p>`;
                info += `<p>chrome.storage: ${!!chrome.storage}</p>`;
                info += `<p>chrome.downloads: ${!!chrome.downloads}</p>`;
            }
            
            debugDiv.innerHTML = info;
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkExtension();
        };

        // 测试状态更新函数
        function updateTestStatus(testId, status, message) {
            const statusEl = document.getElementById(testId + '-status');
            if (statusEl) {
                statusEl.textContent = message || (status === 'pass' ? '通过' : '失败');
                statusEl.className = 'status ' + status;
            }
        }

        // 模拟测试结果（实际使用时需要手动更新）
        function simulateTests() {
            setTimeout(() => updateTestStatus('popup', 'pass'), 1000);
            setTimeout(() => updateTestStatus('search', 'pass'), 2000);
            setTimeout(() => updateTestStatus('collection', 'pass'), 3000);
            setTimeout(() => updateTestStatus('export', 'pass'), 4000);
        }
    </script>
</body>
</html>
